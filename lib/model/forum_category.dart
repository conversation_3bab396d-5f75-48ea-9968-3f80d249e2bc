/// 论坛分类数据模型
class ForumCategory {
  final int pid;
  final int categoryId;
  final String name;
  final String description;
  final String icon;
  final int sort;
  final int property;
  final int threadCount;
  final int parentid;
  final int status;
  final int stype;
  final bool canCreateThread;
  final List<int> searchIds;
  final List<ForumCategory> children;
  final String? threadStyle;

  ForumCategory({
    required this.pid,
    required this.categoryId,
    required this.name,
    required this.description,
    required this.icon,
    required this.sort,
    required this.property,
    required this.threadCount,
    required this.parentid,
    required this.status,
    required this.stype,
    required this.canCreateThread,
    required this.searchIds,
    required this.children,
    this.threadStyle,
  });

  factory ForumCategory.fromJson(Map<String, dynamic> json) {
    return ForumCategory(
      pid: json['pid'] ?? 0,
      categoryId: json['categoryId'] ?? 0,
      name: json['name'] ?? '',
      description: json['description'] ?? '',
      icon: json['icon'] ?? '',
      sort: json['sort'] ?? 0,
      property: json['property'] ?? 0,
      threadCount: json['threadCount'] ?? 0,
      parentid: json['parentid'] ?? 0,
      status: json['status'] ?? 0,
      stype: json['stype'] ?? 0,
      canCreateThread: json['canCreateThread'] ?? false,
      searchIds: List<int>.from(json['searchIds'] ?? []),
      children: (json['children'] as List<dynamic>? ?? [])
          .map((child) => ForumCategory.fromJson(child))
          .toList(),
      threadStyle: json['threadStyle'] != null ? json['threadStyle'].toString() : null,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'pid': pid,
      'categoryId': categoryId,
      'name': name,
      'description': description,
      'icon': icon,
      'sort': sort,
      'property': property,
      'threadCount': threadCount,
      'parentid': parentid,
      'status': status,
      'stype': stype,
      'canCreateThread': canCreateThread,
      'searchIds': searchIds,
      'children': children.map((child) => child.toJson()).toList(),
      'threadStyle': threadStyle,
    };
  }
}