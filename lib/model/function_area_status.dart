class FunctionAreaStatus {
  final String areaModule;
  final String badgeUrl;

  FunctionAreaStatus({
    required this.areaModule,
    required this.badgeUrl,
  });

  factory FunctionAreaStatus.fromJson(Map<String, dynamic> json) {
    return FunctionAreaStatus(
      areaModule: json['area_module'] ?? '',
      badgeUrl: json['badge_url'] ?? '',
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'area_module': areaModule,
      'badge_url': badgeUrl,
    };
  }
}