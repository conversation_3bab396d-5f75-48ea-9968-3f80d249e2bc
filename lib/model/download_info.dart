import 'dart:async';
import 'package:flutter/cupertino.dart';

class DownloadInfo {
  final String name;
  final String url;
  String? size;
  String? fileName;
  String? directoryPath;

  // 添加进度通知器
  final ValueNotifier<String> progressNotifier;
  final ValueNotifier<String> downloadSpeedNotifier;
  final ValueNotifier<String> downloadStatusNotifier;
  double? cacheProgress;
  Timer? progressTimer;

  DownloadInfo({
    required this.name,
    required this.url,
    this.fileName,
    this.directoryPath,
    String progress = "0",
    String downloadStatus = "下载",
    String downloadSpeed = "0",
  }) : progressNotifier = ValueNotifier(progress),
        downloadStatusNotifier = ValueNotifier(downloadStatus),
        downloadSpeedNotifier = ValueNotifier(downloadSpeed);
}