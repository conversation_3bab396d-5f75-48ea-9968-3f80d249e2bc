import 'package:flutter/cupertino.dart';
import 'package:flutter/foundation.dart';
import '../model/game_circle_config.dart';
import '../net/api/game_circle_service.dart';
import '../utils/log_util.dart';

/// 游戏圈配置管理
/// 负责管理当前圈子的配置信息
class GameCircleConfigProvider extends ChangeNotifier {
  GameCircleConfig? _circleConfig;
  bool _isLoading = false;
  String? _error;

  // Getters
  GameCircleConfig? get circleConfig => _circleConfig;
  bool get isLoading => _isLoading;
  String? get error => _error;
  bool get hasConfig => _circleConfig != null;

  /// 根据圈子ID和游戏ID获取配置
  Future<void> loadCircleConfig({
    required String circleId,
    required String tgid,
    BuildContext? context,
  }) async {
    if (_isLoading) return;

    _isLoading = true;
    _error = null;
    notifyListeners();

    try {
      LogUtil.d('开始加载圈子配置: circleId=$circleId, tgid=$tgid');
      GameCircleService.context = context;
      final response = await GameCircleService.getGameCircleConfig(
        context: context,
        circleId: circleId,
        tgid: tgid,
      );

      if (response.success && response.data != null) {
        _circleConfig = response.data!;
        _error = null;
        LogUtil.d('圈子配置加载成功: ${_circleConfig!}');
      } else {
        _error = response.message ?? '加载圈子配置失败';
        LogUtil.e('圈子配置加载失败: $_error');
      }
    } catch (e) {
      _error = '网络异常: $e';
      LogUtil.e('圈子配置加载异常: $e');
    } finally {
      _isLoading = false;
      notifyListeners();
    }
  }

  /// 清除配置
  void clearConfig() {
    _circleConfig = null;
    _error = null;
    _isLoading = false;
    notifyListeners();
  }

  /// 检查圈子类型是否需要显示功能按钮
  bool get shouldShowFunctionButtons {
    return _circleConfig?.circleType == '1';
  }

  /// 检查圈子类型是否需要显示用户区域
  bool get shouldShowUserSection {
    return _circleConfig?.circleType == '1';
  }

  /// 获取游戏图标URL
  String? get gameIcon => _circleConfig?.circleIcon;

  /// 获取游戏名称
  String? get gameName => _circleConfig?.displayName;

  /// 获取背景图URL
  String? get backgroundImage => _circleConfig?.circleTopBanner;

  /// 获取角色图标URL
  String? get characterIcon => _circleConfig?.characterIcon;
}