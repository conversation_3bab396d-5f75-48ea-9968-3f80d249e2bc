import 'package:flutter/material.dart';
import '../../net/api/init_service.dart';
import '../update/update_dialog.dart';
import '../../utils/log_util.dart';

class UpdateCheckService {
  // 更新类型常量
  static const String TYPE_NORMAL = "1"; // 正常，不需要更新
  static const String TYPE_UPDATE = "2"; // 普通更新
  static const String TYPE_FORCE = "3"; // 强制更新

  /// 检查是否需要强更
  static Future<void> checkForUpdate(BuildContext context) async {
    try {
      LogUtil.d('开始检查强更...');
      final mResponse = await InitService.mActivate(context: context);

      if (mResponse.success && mResponse.data != null) {
        LogUtil.d('M层激活成功，检查更新配置');
        await _checkUpdateConfig(context, mResponse.data);
      } else {
        LogUtil.d('M层激活失败: ${mResponse.message}');
        if (context.mounted) {
          _showLatestVersionDialog(context);
        }
      }
    } catch (e) {
      LogUtil.e('检查强更失败: $e');
      if (context.mounted) {
        _showLatestVersionDialog(context);
      }
    }
  }

  /// 检查更新配置
  static Future<void> _checkUpdateConfig(BuildContext context, dynamic data) async {
    try {
      // 检查是否有更新类型字段
      if (data.utype != null && data.utype!.isNotEmpty) {
        final updateType = data.utype!;
        final apkUrl = data.uurl ?? '';
        final updateContent = data.uct ?? '';
        const version = "1.0";

        LogUtil.d('检查更新配置: updateType=$updateType, apkUrl=$apkUrl');

        if (updateType == TYPE_NORMAL) {
          // 正常，不需要更新
          LogUtil.d('当前版本正常，无需更新');
          if (context.mounted) {
            _showLatestVersionDialog(context);
          }
        } else if (updateType == TYPE_UPDATE) {
          // 普通更新
          LogUtil.d('发现可用更新，显示更新弹窗');
          if (context.mounted) {
            _showUpdateDialog(
              context,
              false,
              updateContent,
              apkUrl,
              version,
            );
          }
        } else if (updateType == TYPE_FORCE) {
          // 强制更新
          LogUtil.d('发现强制更新，显示强制更新弹窗');
          if (context.mounted) {
            _showUpdateDialog(
              context,
              true,
              updateContent,
              apkUrl,
              version,
            );
          }
        }
      } else {
        if (context.mounted) {
          _showLatestVersionDialog(context);
        }
      }
    } catch (e) {
      LogUtil.e('检查更新配置失败: $e');
      if (context.mounted) {
        _showLatestVersionDialog(context);
      }
    }
  }

  /// 显示更新弹窗
  static void _showUpdateDialog(
    BuildContext context,
    bool isForce,
    String updateContent,
    String apkUrl,
    String version,
  ) {
    LogUtil.d('准备显示更新弹窗: isForce=$isForce, apkUrl=$apkUrl, version=$version');
    LogUtil.d('更新内容: $updateContent');

    try {
      // 使用 VersionUpdateDialog 组件
      VersionUpdateHelper.showVersionUpdateDialog(
        context,
        exitAppOnClose: isForce, // 强制更新时关闭弹窗会退出应用
        downloadUrl: apkUrl,
        message:
            updateContent.isNotEmpty ? updateContent : '发现新版本，建议立即更新以获得更好的体验。',
        version: version,
      );

      LogUtil.d('更新弹窗显示调用完成');
    } catch (e) {
      LogUtil.e('显示更新弹窗失败: $e');
    }
  }

  /// 显示"当前已是最新版本"弹窗
  static void _showLatestVersionDialog(BuildContext context) {
    showDialog(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          title: const Text('版本检查'),
          content: const Text('当前已是最新版本'),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(12),
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.pop(context),
              child: const Text(
                '确定',
                style: TextStyle(color: Color(0xFF007AFF)),
              ),
            ),
          ],
        );
      },
    );
  }
}