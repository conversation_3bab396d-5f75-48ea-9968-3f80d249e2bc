import 'dart:io';
import 'package:flutter/material.dart';
import 'package:path_provider/path_provider.dart';
import '../../utils/log_util.dart';

class CacheManagementService {
  /// 计算缓存大小
  static Future<String> calculateCacheSize() async {
    try {
      LogUtil.d('开始计算缓存大小...');
      
      int totalSize = 0;
      
      // 获取临时目录（图片缓存通常存储在这里）
      final tempDir = await getTemporaryDirectory();
      if (await tempDir.exists()) {
        totalSize += await _calculateDirectorySize(tempDir);
      }
      
      // 获取应用缓存目录
      try {
        final cacheDir = await getApplicationCacheDirectory();
        if (await cacheDir.exists()) {
          totalSize += await _calculateDirectorySize(cacheDir);
        }
      } catch (e) {
        LogUtil.w('获取应用缓存目录失败: $e');
      }
      
      // 格式化大小显示
      final formattedSize = _formatBytes(totalSize);
      LogUtil.d('缓存大小计算完成: $formattedSize ($totalSize bytes)');
      
      return formattedSize;
    } catch (e) {
      LogUtil.e('计算缓存大小失败: $e');
      return '0 MB';
    }
  }
  
  /// 计算目录大小
  static Future<int> _calculateDirectorySize(Directory directory) async {
    int totalSize = 0;
    
    try {
      await for (final FileSystemEntity entity in directory.list(recursive: true)) {
        if (entity is File) {
          try {
            final stat = await entity.stat();
            totalSize += stat.size;
          } catch (e) {
            // 忽略单个文件的错误
            LogUtil.w('获取文件大小失败: ${entity.path}, $e');
          }
        }
      }
    } catch (e) {
      LogUtil.w('遍历目录失败: ${directory.path}, $e');
    }
    
    return totalSize;
  }
  
  /// 格式化字节大小
  static String _formatBytes(int bytes) {
    if (bytes < 1024) {
      return '$bytes B';
    } else if (bytes < 1024 * 1024) {
      return '${(bytes / 1024).toStringAsFixed(2)} KB';
    } else if (bytes < 1024 * 1024 * 1024) {
      return '${(bytes / (1024 * 1024)).toStringAsFixed(2)} MB';
    } else {
      return '${(bytes / (1024 * 1024 * 1024)).toStringAsFixed(2)} GB';
    }
  }
  
  /// 清理缓存
  static Future<bool> clearCache() async {
    try {
      LogUtil.d('开始清理缓存...');
      
      bool success = true;
      
      // 清理临时目录
      final tempDir = await getTemporaryDirectory();
      if (await tempDir.exists()) {
        success &= await _clearDirectory(tempDir);
      }
      
      // 清理应用缓存目录
      try {
        final cacheDir = await getApplicationCacheDirectory();
        if (await cacheDir.exists()) {
          success &= await _clearDirectory(cacheDir);
        }
      } catch (e) {
        LogUtil.w('清理应用缓存目录失败: $e');
      }
      
      LogUtil.d('缓存清理完成，结果: ${success ? "成功" : "部分失败"}');
      return success;
    } catch (e) {
      LogUtil.e('清理缓存失败: $e');
      return false;
    }
  }
  
  /// 清理目录
  static Future<bool> _clearDirectory(Directory directory) async {
    try {
      final entities = directory.listSync(recursive: true);
      bool allSuccess = true;
      
      for (final entity in entities) {
        try {
          if (entity is File) {
            await entity.delete();
            LogUtil.d('删除文件: ${entity.path}');
          } else if (entity is Directory) {
            await entity.delete(recursive: true);
            LogUtil.d('删除目录: ${entity.path}');
          }
        } catch (e) {
          LogUtil.w('删除失败: ${entity.path}, $e');
          allSuccess = false;
        }
      }
      
      return allSuccess;
    } catch (e) {
      LogUtil.w('清理目录失败: ${directory.path}, $e');
      return false;
    }
  }
  
  /// 显示清理缓存确认弹窗
  static Future<void> showClearCacheDialog(BuildContext context, String cacheSize) async {
    final shouldClear = await showDialog<bool>(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          title: const Text('清理缓存'),
          content: Text('确定要清理缓存吗'),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(12),
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.pop(context, false),
              child: const Text(
                '取消',
                style: TextStyle(color: Color(0xFF999999)),
              ),
            ),
            TextButton(
              onPressed: () => Navigator.pop(context, true),
              child: const Text(
                '确定',
                style: TextStyle(color: Color(0xFF007AFF)),
              ),
            ),
          ],
        );
      },
    );
    
    if (shouldClear == true && context.mounted) {
      await _performCacheClearing(context);
    }
  }
  
  /// 执行缓存清理
  static Future<void> _performCacheClearing(BuildContext context) async {
    // 显示加载对话框
    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (BuildContext context) {
        return const AlertDialog(
          content: Row(
            children: [
              CircularProgressIndicator(),
              SizedBox(width: 20),
              Text('正在清理缓存...'),
            ],
          ),
        );
      },
    );
    
    try {
      final success = await clearCache();
      
      if (context.mounted) {
        // 关闭加载对话框
        Navigator.pop(context);
        
        // 显示结果
        final message = success ? '缓存清理成功' : '缓存清理完成，部分文件可能无法删除';
        final color = success ? Colors.green : Colors.orange;
        
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(message),
            backgroundColor: color,
            duration: const Duration(seconds: 2),
          ),
        );
      }
    } catch (e) {
      if (context.mounted) {
        // 关闭加载对话框
        Navigator.pop(context);
        
        // 显示错误
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('缓存清理失败: $e'),
            backgroundColor: Colors.red,
            duration: const Duration(seconds: 3),
          ),
        );
      }
    }
  }
}