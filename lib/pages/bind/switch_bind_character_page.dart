import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'character_bind_page.dart';
import '../../components/game_authorization_dialog.dart';
import '../../providers/user_provider.dart';
import '../../providers/game_circle_provider.dart';
import '../../providers/game_circle_config_provider.dart';
import '../../model/user.dart';
import '../../model/sub_user.dart';
import '../../services/bind_account_service.dart';
import '../../model/game_role.dart';

class SwitchBindCharacterPage extends StatefulWidget {
  const SwitchBindCharacterPage({super.key});

  @override
  State<SwitchBindCharacterPage> createState() => _SwitchBindCharacterPageState();
}

class _SwitchBindCharacterPageState extends State<SwitchBindCharacterPage> {
  final BindAccountService _bindAccountService = BindAccountService();
  List<SubUserItem> _subUsers = [];
  bool _isLoadingSubUsers = true;

  @override
  void initState() {
    super.initState();
    _loadSubUsers();
  }

  /// 加载关联账号列表
  Future<void> _loadSubUsers() async {
    try {
      final response = await _bindAccountService.getSubUserList();
      
      // 直接检查SubUserListResponse的state字段
      if (response.data != null && response.data!.state == 1 && response.data!.data?.list != null) {
        setState(() {
          _subUsers = response.data!.data!.list;
          _isLoadingSubUsers = false;
        });
        debugPrint('成功获取关联账号列表，共${_subUsers.length}个账号');
      } else {
        debugPrint('获取关联账号列表失败: state=${response.data?.state}, msg=${response.data?.msg}');
        setState(() {
          _subUsers = [];
          _isLoadingSubUsers = false;
        });
      }
    } catch (e) {
      debugPrint('获取关联账号列表异常: $e');
      setState(() {
        _subUsers = [];
        _isLoadingSubUsers = false;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.grey[50],
      appBar: AppBar(
        backgroundColor: Colors.white,
        elevation: 0,
        leading: IconButton(
          icon: const Icon(Icons.arrow_back, color: Colors.black),
          onPressed: () => Navigator.pop(context),
        ),
        title: const Text(
          '选择游戏账号绑定角色',
          style: TextStyle(
            color: Colors.black,
            fontSize: 18,
            fontWeight: FontWeight.w500,
          ),
        ),
        centerTitle: true,
      ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // 37手游官服账号
            const Text(
              '37手游官服账号',
              style: TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.w500,
                color: Colors.black87,
              ),
            ),
            const SizedBox(height: 12),
            
            // 账号列表
            if (_isLoadingSubUsers) ...[
              Container(
                padding: const EdgeInsets.all(16),
                decoration: BoxDecoration(
                  color: Colors.white,
                  borderRadius: BorderRadius.circular(8),
                ),
                child: const Row(
                  children: [
                    CircularProgressIndicator(),
                    SizedBox(width: 16),
                    Text(
                      '正在加载账号列表...',
                      style: TextStyle(
                        fontSize: 14,
                        color: Colors.grey,
                      ),
                    ),
                  ],
                ),
              ),
            ] else if (_subUsers.isNotEmpty) ...[
              ..._subUsers.map((subUser) => _buildSubUserAccountItem(subUser)),
            ] else ...[
              Container(
                padding: const EdgeInsets.all(16),
                decoration: BoxDecoration(
                  color: Colors.white,
                  borderRadius: BorderRadius.circular(8),
                ),
                child: const Text(
                  '暂无关联账号',
                  style: TextStyle(
                    fontSize: 14,
                    color: Colors.grey,
                  ),
                ),
              ),
            ],
            
            const SizedBox(height: 12),
            
            // 其他绑定方式
            const Text(
              '其他绑定方式',
              style: TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.w500,
                color: Colors.black87,
              ),
            ),
            const SizedBox(height: 12),
            
            // 游戏图标和名称 - 使用圈子配置
            Consumer<GameCircleConfigProvider>(
              builder: (context, configProvider, child) {
                final gameIcon = configProvider.gameIcon;
                final gameName = configProvider.gameName;

                return Center(
                  child: Column(
                    children: [
                      Container(
                        width: 70,
                        height: 70,
                        decoration: BoxDecoration(
                          borderRadius: BorderRadius.circular(14),
                          boxShadow: [
                            BoxShadow(
                              color: Colors.grey.withOpacity(0.2),
                              spreadRadius: 1,
                              blurRadius: 4,
                              offset: const Offset(0, 2),
                            ),
                          ],
                        ),
                        child: ClipRRect(
                          borderRadius: BorderRadius.circular(14),
                          child: gameIcon != null
                              ? Image.network(
                                  gameIcon,
                                  fit: BoxFit.cover,
                                  errorBuilder: (context, error, stackTrace) {
                                    return Container(
                                      color: Colors.grey[200],
                                      child: const Icon(
                                        Icons.games,
                                        size: 35,
                                        color: Colors.grey,
                                      ),
                                    );
                                  },
                                )
                              : Container(
                                  color: Colors.grey[200],
                                  child: const Icon(
                                    Icons.games,
                                    size: 35,
                                    color: Colors.grey,
                                  ),
                                ),
                        ),
                      ),
                      const SizedBox(height: 8),
                      Text(
                        gameName ?? '斗罗大陆：魂师对决',
                        style: const TextStyle(
                          fontSize: 15,
                          fontWeight: FontWeight.w500,
                          color: Colors.black87,
                        ),
                      ),
                    ],
                  ),
                );
              },
            ),
            
            const SizedBox(height: 24),
            
            // 拉起游戏授权绑定按钮
            Stack(
              clipBehavior: Clip.none,
              children: [
                Container(
                  width: double.infinity,
                  child: ElevatedButton(
                    onPressed: _launchGameAuthorization,
                    style: ElevatedButton.styleFrom(
                      backgroundColor: Colors.blue[600],
                      foregroundColor: Colors.white,
                      padding: const EdgeInsets.symmetric(vertical: 14),
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(8),
                      ),
                      elevation: 0,
                    ),
                    child: Row(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        Image.asset(
                            'assets/images/games_icon_white.png',
                            width: 18,
                            height: 18,
                          ),
                        const SizedBox(width: 6),
                        const Text(
                          '拉起游戏授权绑定',
                          style: TextStyle(
                            fontSize: 15,
                            fontWeight: FontWeight.w500,
                          ),
                        ),
                      ],
                    ),
                  ),
                ),
                // 叠在右上角的标签
                Positioned(
                  top: -8,
                  right: 8,
                  child: Container(
                    padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 3),
                    decoration: BoxDecoration(
                      color: Colors.red,
                      borderRadius: BorderRadius.circular(10),
                    ),
                    child: const Text(
                      '非37官方账号推荐使用',
                      style: TextStyle(
                        color: Colors.white,
                        fontSize: 9,
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                  ),
                ),
              ],
            ),
            
            const SizedBox(height: 8),
            
            // 底部提示文字
            const Center(
              child: Text(
                '选择授权游戏包后游戏内点击【确定】',
                style: TextStyle(
                  fontSize: 11,
                  color: Colors.grey,
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildSubUserAccountItem(SubUserItem subUser) {
    return Container(
      margin: const EdgeInsets.only(bottom: 8),
      child: GestureDetector(
        onTap: () => _selectSubUserAccount(subUser),
        child: Container(
          padding: const EdgeInsets.all(14),
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.circular(8),
            boxShadow: [
              BoxShadow(
                color: Colors.grey.withOpacity(0.1),
                spreadRadius: 1,
                blurRadius: 3,
                offset: const Offset(0, 1),
              ),
            ],
          ),
          child: Row(
            children: [
              Expanded(
                child: Text(
                  subUser.uname,
                  style: const TextStyle(
                    fontSize: 15,
                    color: Colors.black87,
                  ),
                ),
              ),
              if (subUser.isCurrent)
                Container(
                  padding: const EdgeInsets.symmetric(horizontal: 10, vertical: 4),
                  decoration: BoxDecoration(
                    color: Colors.blue[600],
                    borderRadius: BorderRadius.circular(14),
                  ),
                  child: const Text(
                    '当前登录',
                    style: TextStyle(
                      fontSize: 11,
                      color: Colors.white,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                )
              else
                Icon(
                  Icons.arrow_forward_ios,
                  size: 14,
                  color: Colors.grey[600],
                ),
            ],
          ),
        ),
      ),
    );
  }

  /// 获取用户已绑定的角色列表
  Future<List<GameRoleV2>?> _getBoundUserRoles(User user) async {
    try {
      // 获取当前游戏圈子的tgid
      String tgid = '37'; // 默认值
      try {
        final gameCircleProvider = Provider.of<GameCircleProvider>(context, listen: false);
        if (gameCircleProvider.hasSelectedGameCircle && 
            gameCircleProvider.selectedGameCircle != null) {
          tgid = gameCircleProvider.selectedGameCircle!.tgid;
        }
      } catch (e) {
        // 如果获取失败，使用默认值
        debugPrint('获取当前游戏圈子tgid失败，使用默认值: $e');
      }

      // 调用角色列表V2接口
      final response = await _bindAccountService.getRoleListV2(
        tgid: tgid,
      );

      if (response.success && response.data?.data?.list != null) {
        return response.data!.data!.list;
      } else {
        debugPrint('获取角色列表失败: ${response.message}');
        return [];
      }
    } catch (e) {
      debugPrint('获取角色列表异常: $e');
      return null;
    }
  }

  /// 获取用户所有角色列表
  Future<List<GameRole>?> _getAllUserRoles(String uid) async {
    try {
      // 获取当前游戏圈子的tgid
      String tgid = '37'; // 默认值
      try {
        final gameCircleProvider = Provider.of<GameCircleProvider>(context, listen: false);
        if (gameCircleProvider.hasSelectedGameCircle && 
            gameCircleProvider.selectedGameCircle != null) {
          tgid = gameCircleProvider.selectedGameCircle!.tgid;
        }
      } catch (e) {
        // 如果获取失败，使用默认值
        debugPrint('获取当前游戏圈子tgid失败，使用默认值: $e');
      }

      // 调用角色列表接口（获取所有角色）
      final response = await _bindAccountService.getRoleList(
        tgid: tgid,
        uid: uid,
      );

      if (response.success && response.data != null) {
        return response.data!;
      } else {
        debugPrint('获取所有角色列表失败: ${response.message}');
        return [];
      }
    } catch (e) {
      debugPrint('获取所有角色列表异常: $e');
      return null;
    }
  }

  /// 检查可绑定的角色
  /// 返回可绑定的角色列表，如果查询失败返回null
  Future<List<GameRole>?> _checkAvailableRoles(String uid) async {
    try {
      final userProvider = Provider.of<UserProvider>(context, listen: false);
      final currentUser = userProvider.currentUser;
      
      if (currentUser == null) {
        debugPrint('当前用户为空，无法查询绑定角色');
        return null;
      }

      // 并行查询所有角色和已绑定角色
      final results = await Future.wait([
        _getAllUserRoles(uid),
        _getBoundUserRoles(currentUser),
      ]);

      final allRoles = results[0] as List<GameRole>?;
      final boundRoles = results[1] as List<GameRoleV2>?;

      // 如果任一查询失败，返回null
      if (allRoles == null || boundRoles == null) {
        return null;
      }

      // 如果没有角色，返回空列表
      if (allRoles.isEmpty) {
        return [];
      }

      // 如果没有已绑定的角色，所有角色都可绑定
      if (boundRoles.isEmpty) {
        return allRoles;
      }

      // 创建已绑定角色的ID集合，用于快速查找
      final boundRoleIds = boundRoles.map((role) => role.drid.toString()).toSet();

      // 过滤出未绑定的角色
      final availableRoles = allRoles.where((role) {
        return !boundRoleIds.contains(role.drid);
      }).toList();

      return availableRoles;
    } catch (e) {
      debugPrint('检查可绑定角色异常: $e');
      return null;
    }
  }

  void _selectSubUserAccount(SubUserItem subUser) async {
    // 显示加载状态
    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (BuildContext context) {
        return const AlertDialog(
          content: Row(
            children: [
              CircularProgressIndicator(),
              SizedBox(width: 16),
              Text('正在查询角色列表...'),
            ],
          ),
        );
      },
    );

    try {
      // 查询可绑定的角色列表
      final availableRoles = await _checkAvailableRoles(subUser.uid);
      
      // 确保组件仍然挂载
      if (!mounted) return;
      
      // 关闭加载对话框
      Navigator.of(context).pop();

      if (availableRoles == null) {
        // 查询失败，显示错误信息
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('查询角色列表失败，请稍后重试'),
            backgroundColor: Colors.red,
          ),
        );
        return;
      }

      // 不管是否有可绑定角色，都跳转到角色绑定页面
      // 获取当前用户的已绑定角色列表
      final userProvider = Provider.of<UserProvider>(context, listen: false);
      final currentUser = userProvider.currentUser;
      final allUserRoles = await _getAllUserRoles(subUser.uid);
      final boundRoles = currentUser != null ? await _getBoundUserRoles(currentUser) : <GameRoleV2>[];
      
      if (!mounted) return;
      
      Navigator.push(
        context,
        MaterialPageRoute(
          builder: (context) => CharacterBindPage(
            phoneNumber: subUser.uname,
            allRoles: allUserRoles ?? [],
            boundRoles: boundRoles ?? [],
            uid: subUser.uid,
          ),
        ),
      );
    } catch (e) {
      // 确保组件仍然挂载
      if (!mounted) return;
      
      // 关闭加载对话框
      Navigator.of(context).pop();
      
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('查询角色列表异常: $e'),
          backgroundColor: Colors.red,
        ),
      );
    }
  }

  void _selectAccount(User user, bool isCurrentUser) async {
    if (isCurrentUser) {
      // 当前登录账号，需要检查角色列表
      await _handleCurrentUserSelection(user.muid ?? '', user.loginInfo.muname ?? '');
      return;
    }
    
    // 其他账号，可以切换账号或跳转到空绑定页面
    _showAccountSwitchDialog(user);
  }

  /// 处理当前用户选择逻辑
  Future<void> _handleCurrentUserSelection(String uid, String displayName) async {
    // 显示加载状态
    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (BuildContext context) {
        return const AlertDialog(
          content: Row(
            children: [
              CircularProgressIndicator(),
              SizedBox(width: 16),
              Text('正在查询角色列表...'),
            ],
          ),
        );
      },
    );

    try {
      // 查询可绑定的角色列表
      final availableRoles = await _checkAvailableRoles(uid);
      
      // 确保组件仍然挂载
      if (!mounted) return;
      
      // 关闭加载对话框
      Navigator.of(context).pop();

      if (availableRoles == null) {
        // 查询失败，显示错误信息
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('查询角色列表失败，请稍后重试'),
            backgroundColor: Colors.red,
          ),
        );
        return;
      }

      // 不管是否有可绑定角色，都跳转到角色绑定页面
      // 获取当前用户的已绑定角色列表
      final allUserRoles = await _getAllUserRoles(uid);
      final userProvider = Provider.of<UserProvider>(context, listen: false);
      final currentUser = userProvider.currentUser;
      final boundRoles = currentUser != null ? await _getBoundUserRoles(currentUser) : <GameRoleV2>[];
      
      if (!mounted) return;
      
      Navigator.push(
        context,
        MaterialPageRoute(
          builder: (context) => CharacterBindPage(
            phoneNumber: displayName,
            uid: uid,
            allRoles: allUserRoles ?? [],
            boundRoles: boundRoles ?? [],
          ),
        ),
      );
    } catch (e) {
      // 确保组件仍然挂载
      if (!mounted) return;
      
      // 关闭加载对话框
      Navigator.of(context).pop();
      
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('查询角色列表异常: $e'),
          backgroundColor: Colors.red,
        ),
      );
    }
  }

  void _showAccountSwitchDialog(User user) {
    showDialog(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          title: const Text('切换账号'),
          content: Text('是否切换到账号 ${user.loginInfo.muname ?? ''}？'),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(),
              child: const Text('取消'),
            ),
            TextButton(
              onPressed: () {
                Navigator.of(context).pop();
                _performAccountSwitch(user);
              },
              child: const Text('确定'),
            ),
          ],
        );
      },
    );
  }

  void _performAccountSwitch(User user) async {
    final userProvider = Provider.of<UserProvider>(context, listen: false);
    
    // 显示加载状态
    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (BuildContext context) {
        return const AlertDialog(
          content: Row(
            children: [
              CircularProgressIndicator(),
              SizedBox(width: 16),
              Text('正在切换账号...'),
            ],
          ),
        );
      },
    );
    
    try {
      final result = await userProvider.switchUser(user);
      final success = result.$1;
      final errorMessage = result.$2;
      
      // 确保组件仍然挂载
      if (!mounted) return;
      
      // 关闭加载对话框
      Navigator.of(context).pop();
      
      if (success) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('已切换到账号 ${user.loginInfo.muname ?? ''}'),
            backgroundColor: Colors.green,
          ),
        );
        
        // 刷新页面状态
        setState(() {});
      } else {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(errorMessage ?? '切换到账号 ${user.loginInfo.muname ?? ''} 失败，请重新登录'),
            backgroundColor: Colors.red,
          ),
        );
      }
    } catch (e) {
      // 确保组件仍然挂载
      if (!mounted) return;
      
      // 关闭加载对话框
      Navigator.of(context).pop();
      
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('切换账号失败: $e'),
          backgroundColor: Colors.red,
        ),
      );
    }
  }

  void _launchGameAuthorization() {
    GameAuthorizationDialog.show(
      context: context,
      onClose: () {
        // 弹窗关闭时的回调，可以在这里处理一些逻辑
        debugPrint('游戏授权绑定弹窗已关闭');
      },
    );
  }
}