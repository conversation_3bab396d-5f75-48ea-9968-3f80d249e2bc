import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:cached_network_image/cached_network_image.dart';
import 'dart:convert';

import '../../common/dl_color.dart';
import '../../components/cache_image.dart';
import '../../model/forum_post_list.dart';
import '../../model/topic_list.dart';
import '../../net/api/forum_service.dart';
import '../../net/config/http_base_config.dart';
import 'community_detail_page.dart';
import 'user_profile_page.dart';

/// 话题页面
class TopicPage extends StatefulWidget {
  final String topicName;
  final int? topicId;
  final String? description;
  final String? avatarUrl;
  final int viewCount;
  final int likeCount;
  final int threadCount;

  const TopicPage({
    super.key,
    required this.topicName,
    this.topicId,
    this.description,
    this.avatarUrl,
    this.viewCount = 0,
    this.likeCount = 0,
    this.threadCount = 0,
  });

  @override
  State<TopicPage> createState() => _TopicPageState();
}

class _TopicPageState extends State<TopicPage> {
  final ForumService _forumService = ForumService();
  List<ForumPost> _posts = [];
  TopicData? _topicData;
  bool _isLoading = true;
  bool _isLoadingMore = false;
  String? _error;
  int _currentPage = 1;
  int _totalPage = 1;
  bool _hasMore = true;
  final ScrollController _scrollController = ScrollController();

  @override
  void initState() {
    super.initState();
    // 设置沉浸式状态栏
    SystemChrome.setEnabledSystemUIMode(SystemUiMode.edgeToEdge);
    _loadTopicData(reset: true); // 首次加载使用reset保证正确初始化
    _scrollController.addListener(_onScroll);
  }

  @override
  void dispose() {
    _scrollController.removeListener(_onScroll);
    _scrollController.dispose();
    super.dispose();
  }

  void _onScroll() {
    if (_scrollController.position.pixels >= _scrollController.position.maxScrollExtent - 200) {
      if (!_isLoadingMore && _hasMore) {
        _loadTopicData();
      }
    }
  }

  Future<void> _loadTopicData({bool reset = false}) async {
    final int targetTopicId = widget.topicId ?? _inferTopicIdFromName(widget.topicName);
    
    debugPrint('TopicPage: 开始加载topic数据');
    debugPrint('TopicPage: widget.topicId = ${widget.topicId}');
    debugPrint('TopicPage: widget.topicName = "${widget.topicName}"');
    debugPrint('TopicPage: targetTopicId = $targetTopicId');
    debugPrint('TopicPage: reset = $reset');
    
    try {
      if (reset) {
        setState(() {
          _isLoading = true; // reset时应该显示加载状态
          _error = null;
          _currentPage = 1;
          _hasMore = true;
        });
      } else {
        if (!_hasMore) return;
        setState(() {
          _isLoadingMore = true;
        });
      }

      final response = await _forumService.getTopicList(
        baseUrl: HttpBaseConfig.forumBaseUrl,
        topicId: targetTopicId,
        page: reset ? 1 : _currentPage + 1,
        perPage: 10,
        context: context
      );

      if (response.code == 0 && response.data != null) {
        final topicList = response.data!;
        setState(() {
          if (reset) {
            _posts.clear();
            if (topicList.pageData.isNotEmpty) {
              _topicData = topicList.pageData.first;
              _posts.addAll(_topicData!.threads);
              // 添加调试信息
              debugPrint('TopicPage: 获取到topic数据 (reset)');
              debugPrint('TopicPage: topic name = ${_topicData!.content}');
              debugPrint('TopicPage: banner url = ${_topicData!.banner}');
              debugPrint('TopicPage: banner title = ${_topicData!.bannerTitle}');
              debugPrint('TopicPage: background url = ${_topicData!.background}');
            }
          } else {
            // 初始加载或加载更多时，也需要设置_topicData
            if (topicList.pageData.isNotEmpty) {
              if (_topicData == null) {
                // 首次加载时设置_topicData
                _topicData = topicList.pageData.first;
                debugPrint('TopicPage: 获取到topic数据 (首次加载)');
                debugPrint('TopicPage: topic name = ${_topicData!.content}');
                debugPrint('TopicPage: banner url = ${_topicData!.banner}');
                debugPrint('TopicPage: banner title = ${_topicData!.bannerTitle}');
                debugPrint('TopicPage: background url = ${_topicData!.background}');
              }
              _posts.addAll(topicList.pageData.first.threads);
            }
          }
          _currentPage = topicList.currentPage;
          _totalPage = topicList.totalPage;
          _hasMore = _currentPage < _totalPage;
          _isLoading = false;
          _isLoadingMore = false;
        });
      } else {
        setState(() {
          _error = response.message ?? '加载失败';
          _isLoading = false;
          _isLoadingMore = false;
        });
      }
    } catch (e) {
      setState(() {
        _error = '网络错误: $e';
        _isLoading = false;
        _isLoadingMore = false;
      });
    }
  }

  int _inferTopicIdFromName(String topicName) {
    // 简单的名称到ID映射，实际项目中可能需要更复杂的逻辑
    final Map<String, int> nameToIdMap = {
      '猎魂集结开战': 374,
      '我要上每周推荐': 325,
      '猎魂上分日记': 375,
      '猎魂巅峰挑战赛': 376,
      '斗魂对决阵容分享': 371,
    };
    return nameToIdMap[topicName] ?? 374; // 默认使用374
  }

  double _getHeaderHeight(BuildContext context) {
    // 计算Header的高度，包括状态栏、导航栏、头像区域和描述区域
    final statusBarHeight = MediaQuery.of(context).padding.top;
    final hasDescription = _getTopicDescription().isNotEmpty;
    return statusBarHeight + 56 + 100 + (hasDescription ? 40 : 0); // 调整描述区域高度
  }

  @override
  Widget build(BuildContext context) {
    return AnnotatedRegion<SystemUiOverlayStyle>(
      value: const SystemUiOverlayStyle(
        statusBarColor: Colors.transparent,
        statusBarIconBrightness: Brightness.light, // Android 状态栏图标为浅色
        statusBarBrightness: Brightness.dark, // iOS 状态栏内容为浅色
        systemNavigationBarColor: Colors.transparent,
        systemNavigationBarIconBrightness: Brightness.light,
      ),
      child: Scaffold(
        backgroundColor: Colors.transparent,
        extendBodyBehindAppBar: true,
        body: _isLoading
            ? const Center(child: CircularProgressIndicator())
            : _error != null
                ? _buildErrorWidget()
                : Stack(
                    children: [
                      Positioned(
                        top: 0,
                        left: 0,
                        right: 0,
                        child: _buildHeader(context),
                      ),
                      Container(
                        padding: const EdgeInsets.fromLTRB(0, 80, 0, 0),
                        child: NestedScrollView(
                          controller: _scrollController,
                          headerSliverBuilder: (context, innerBoxIsScrolled) {
                            return [
                              // 空白区域，用于为固定Header让出空间
                              SliverToBoxAdapter(
                                child: SizedBox(
                                  height: 145,
                                ),
                              ),
                            ];
                          },
                          body: Container(
                            decoration: const BoxDecoration(
                              color: Colors.white,
                              borderRadius: BorderRadius.only(
                                topLeft: Radius.circular(12),
                                topRight: Radius.circular(12),
                              ),
                            ),
                            child: SingleChildScrollView(
                              child: Column(
                                children: [
                                  _buildBannerCard(),
                                  if (_posts.isEmpty && !_isLoading)
                                    _buildEmptyWidget()
                                  else
                                    Column(
                                      children: [
                                        ..._posts.map((post) => _buildPostItem(post)),
                                        if (_isLoadingMore)
                                          const Center(
                                            child: Padding(
                                              padding: EdgeInsets.all(16.0),
                                              child: CircularProgressIndicator(),
                                            ),
                                          ),
                                      ],
                                    ),
                                ],
                              ),
                            ),
                          ),
                        ),
                      )
                    ],
                ),
      ),
    );
  }

  Widget _buildHeader(BuildContext context) {
    return Container(
      height: _getHeaderHeight(context),
      decoration: BoxDecoration(
        image: _getTopicBackgroundUrl().isNotEmpty
            ? DecorationImage(
                image: CachedNetworkImageProvider(_getTopicBackgroundUrl()),
                fit: BoxFit.fill,
                colorFilter: ColorFilter.mode(
                  Colors.black.withOpacity(0.3),
                  BlendMode.darken,
                ),
              )
            : null,
        gradient: _getTopicBackgroundUrl().isEmpty
            ? const LinearGradient(
                begin: Alignment.topCenter,
                end: Alignment.bottomCenter,
                colors: [Color(0xFF6BA3D6), Color(0xFF6BA3D6), Colors.white],
                stops: [0.0, 0.6, 1.0],
              )
            : null,
      ),
      child: SafeArea(
        child: Padding(
          padding: const EdgeInsets.fromLTRB(12, 0, 12, 0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                IconButton(
                  icon: const Icon(Icons.arrow_back_ios_new, color: Colors.white),
                  onPressed: () => Navigator.of(context).pop(),
                ),
                const Spacer(),
                Container(
                  width: 36,
                  height: 36,
                  decoration: BoxDecoration(
                    color: Colors.white.withValues(alpha: 0.2),
                    borderRadius: BorderRadius.circular(18),
                  ),
                  child: const Icon(Icons.more_horiz, color: Colors.white),
                )
              ],
            ),
            Padding(
              padding: const EdgeInsets.only(top: 8),
              child: Row(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Container(
                    width: 64,
                    height: 64,
                    decoration: BoxDecoration(
                      borderRadius: BorderRadius.circular(12),
                      color: Colors.white.withValues(alpha: 0.2),
                    ),
                    clipBehavior: Clip.antiAlias,
                    child: _getTopicAvatarUrl().isEmpty
                        ? Container(color: Colors.white.withValues(alpha: 0.2))
                        : CachedNetworkImage(
                            imageUrl: _getTopicAvatarUrl(),
                            fit: BoxFit.cover,
                          ),
                  ),
                  const SizedBox(width: 12),
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          '#${_getTopicContent()}',
                          style: const TextStyle(
                            color: Colors.white,
                            fontSize: 24,
                            fontWeight: FontWeight.w700,
                          ),
                        ),
                        const SizedBox(height: 8),
                        Text(
                          _buildTopicStats(),
                          style: TextStyle(
                            color: Colors.white.withValues(alpha: 0.9),
                            fontSize: 14,
                            fontWeight: FontWeight.w500,
                          ),
                        ),
                      ],
                    ),
                  ),
                ],
              ),
            ),
            if (_getTopicDescription().isNotEmpty) ...[
              const SizedBox(height: 12),
              Text(
                _getTopicDescription(),
                style: TextStyle(
                  color: Colors.white.withValues(alpha: 0.95),
                  fontSize: 12,
                  height: 1.4,
                ),
                maxLines: 2,
                overflow: TextOverflow.ellipsis,
              ),
            ],
          ],
        ),
      ),
      ),
    );
  }

  Widget _buildBannerCard() {
    return Container(
      margin: const EdgeInsets.fromLTRB(12, 12, 12, 8),
      decoration: const BoxDecoration(
        color: Colors.white,
      ),
      child: GestureDetector(
        onTap: () {
          if (_topicData?.bannerJumpLink.isNotEmpty == true) {
            debugPrint('Banner点击: ${_topicData!.bannerJumpLink}');
          }
        },
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            () {
              final bannerUrl = _topicData?.banner ?? '';
              debugPrint('TopicPage: 正在渲染banner, URL = "$bannerUrl"');
              debugPrint('TopicPage: banner非空 = ${bannerUrl.isNotEmpty}');
              
              if (bannerUrl.isNotEmpty) {
                return CachedNetworkImage(
                  imageUrl: bannerUrl,
                  fit: BoxFit.fitWidth, // 使用fitWidth实现自适应
                  width: double.infinity,
                  placeholder: (c, u) => Container(
                    color: Colors.white,
                    height: 200, // 加载时的占位高度
                    child: const Center(
                      child: CircularProgressIndicator(),
                    ),
                  ),
                  errorWidget: (c, u, e) {
                    debugPrint('TopicPage: Banner图片加载失败: $e');
                    return Container(
                      color: Colors.white,
                      height: 200, // 错误时的占位高度
                      child: Icon(
                        Icons.image_not_supported_outlined,
                        size: 48,
                        color: Colors.grey[400],
                      ),
                    );
                  },
                );
              } else {
                return Container(
                  color: Colors.white,
                  height: 200, // 默认占位高度
                  child: Center(
                    child: Column(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        Icon(
                          Icons.image_outlined,
                          size: 48,
                          color: Colors.grey[400],
                        ),
                        const SizedBox(height: 8),
                        Text(
                          '正在加载活动信息...',
                          style: TextStyle(
                            fontSize: 14,
                            color: Colors.grey[600],
                          ),
                        ),
                      ],
                    ),
                  ),
                );
              }
            }(),
            Padding(
              padding: const EdgeInsets.fromLTRB(12, 10, 12, 12),
              child: Text(
                _getBannerTitle(),
                style: const TextStyle(
                  fontSize: 14,
                  color: Colors.black87,
                  fontWeight: FontWeight.w500,
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildPostItem(ForumPost post) {
    return Column(
      children: [
        // 灰色分割线
        Container(
          height: 8,
          color: Colors.grey[200],
          width: double.infinity,
        ),
        GestureDetector(
          onTap: () => _navigateToPostDetail(post),
          child: Container(
            margin: const EdgeInsets.fromLTRB(12, 12, 12, 0),
            padding: const EdgeInsets.fromLTRB(12, 12, 12, 10),
            decoration: BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.circular(12),
              boxShadow: [
                BoxShadow(color: Colors.black.withOpacity(0.04), blurRadius: 8, offset: const Offset(0, 2)),
              ],
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                _buildUserHeader(post),
                const SizedBox(height: 10),
                if (post.title.isNotEmpty)
                  Text(
                    post.title,
                    style: const TextStyle(fontSize: 16, fontWeight: FontWeight.w700, color: Colors.black87, height: 1.3),
                    maxLines: 2,
                    overflow: TextOverflow.ellipsis,
                  ),
                const SizedBox(height: 6),
                _buildContentPreview(post),
                const SizedBox(height: 10),
                // 话题标签
                _buildTopicTags(post),
                _buildBottomInfo(post),
              ],
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildUserHeader(ForumPost post) {
    return Row(
      children: [
        GestureDetector(
          onTap: () => _navigateToUserProfile(post.user),
          child: CircleAvatar(
            radius: 18,
            backgroundColor: Colors.grey[200],
            backgroundImage: post.user.avatar.isNotEmpty ? NetworkImage(post.user.avatar) : null,
            child: post.user.avatar.isEmpty
                ? Icon(
                    Icons.person,
                    size: 18,
                    color: Colors.grey[600],
                  )
                : null,
          ),
        ),
        const SizedBox(width: 10),
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                post.user.nickname.isNotEmpty ? post.user.nickname : '匿名用户',
                style: const TextStyle(fontSize: 14, fontWeight: FontWeight.w600, color: Colors.black87),
                maxLines: 1,
                overflow: TextOverflow.ellipsis,
              ),
              const SizedBox(height: 2),
              if (post.user.badge.isNotEmpty)
                Container(
                  padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
                  decoration: BoxDecoration(
                    color: Colors.blue[50],
                    borderRadius: BorderRadius.circular(4),
                    border: Border.all(color: Colors.blue[200]!, width: 0.5),
                  ),
                  child: Text(
                    '官方账号',
                    style: TextStyle(fontSize: 10, color: Colors.blue[600], fontWeight: FontWeight.w500),
                  ),
                ),
            ],
          ),
        ),
      ],
    );
  }

  Widget _buildContentPreview(ForumPost post) {
    final String cleanText = _stripHtmlTags(post.content.text);
    final bool needsTruncation = cleanText.length > 102;
    final String displayText = needsTruncation ? '${cleanText.substring(0, 102)}...' : cleanText;

    final List<dynamic> allImages = _getAllImages(post);

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        if (displayText.isNotEmpty)
          Text(
            displayText,
            style: TextStyle(fontSize: 14, color: Colors.grey[800], height: 1.45),
            maxLines: 3,
            overflow: TextOverflow.ellipsis,
          ),
        if (allImages.isNotEmpty) ...[
          const SizedBox(height: 8),
          _buildContentImages(allImages),
        ],
      ],
    );
  }

  List<dynamic> _getAllImages(ForumPost post) {
    final List<dynamic> allImages = [];
    if (post.content.images.isNotEmpty) {
      allImages.addAll(post.content.images);
    }
    final bool isSeparatedContent = post.from != 2 && post.isMixThread != 1;
    if (isSeparatedContent) {
      final indexes = post.content.indexes;
      if (indexes.containsKey('101')) {
        final imageIndex = indexes['101'];
        if (imageIndex is Map<String, dynamic> && imageIndex['body'] is List) {
          final bodyList = imageIndex['body'] as List;
          for (var item in bodyList) {
            if (item is Map<String, dynamic>) {
              final imageUrl = item['url'] ?? item['thumbUrl'] ?? item['attachment'];
              if (imageUrl != null && imageUrl.isNotEmpty) {
                allImages.add(item);
              }
            }
          }
        }
      }
    }
    return allImages;
  }

  Widget _buildContentImages(List<dynamic> images) {
    if (images.isEmpty) return const SizedBox.shrink();
    final int displayCount = images.length > 2 ? 2 : images.length;
    if (displayCount == 1) {
      return _buildSingleImage(images[0], allImages: images);
    } else {
      return _buildImageGrid(images, displayCount);
    }
  }

  Widget _buildSingleImage(dynamic imageData, {List<dynamic>? allImages}) {
    String? imageUrl;
    if (imageData is Map<String, dynamic>) {
      imageUrl = imageData['url'] ?? imageData['thumbUrl'] ?? imageData['attachment'];
    } else if (imageData is String) {
      imageUrl = imageData;
    }
    if (imageUrl == null || imageUrl.isEmpty) return const SizedBox.shrink();
    return ClipRRect(
      borderRadius: BorderRadius.circular(8),
      child: CachedImage(
        imageUrl: imageUrl,
        width: double.infinity,
        height: 200,
        fit: BoxFit.cover,
        onTap: () {},
      ),
    );
  }

  Widget _buildImageGrid(List<dynamic> images, int displayCount) {
    return SizedBox(
      height: 120,
      child: Row(
        children: List.generate(displayCount, (index) {
          final imageData = images[index];
          String? imageUrl;
          if (imageData is Map<String, dynamic>) {
            imageUrl = imageData['url'] ?? imageData['thumbUrl'] ?? imageData['attachment'];
          } else if (imageData is String) {
            imageUrl = imageData;
          }
          return Expanded(
            child: Container(
              margin: EdgeInsets.only(right: index < (displayCount - 1) ? 4 : 0),
              decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(6),
                color: Colors.grey[200],
              ),
              clipBehavior: Clip.antiAlias,
              child: imageUrl != null && imageUrl.isNotEmpty
                  ? CachedImage(
                      imageUrl: imageUrl,
                      width: double.infinity,
                      height: double.infinity,
                      fit: BoxFit.cover,
                    )
                  : Container(color: Colors.grey[200]),
            ),
          );
        }),
      ),
    );
  }

  Widget _buildTopicTags(ForumPost post) {
    // 如果没有话题标签，返回空Widget
    if (post.topics.trim().isEmpty) {
      return const SizedBox.shrink();
    }
    
    List<Map<String, String>> topicList = _parseTopics(post.topics);
    
    if (topicList.isEmpty) {
      return const SizedBox.shrink();
    }
    
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Wrap(
          spacing: 8.0,
          runSpacing: 4.0,
          children: topicList.map((topicData) {
            return GestureDetector(
              onTap: () => _navigateToTopicPage(topicData['id'] ?? topicData['content'] ?? ''),
              child: Container(
                padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                decoration: BoxDecoration(
                  color: Colors.grey[200],
                  borderRadius: BorderRadius.circular(12),
                ),
                child: Text(
                  '#${topicData['content'] ?? topicData['id'] ?? ''}',
                  style: TextStyle(
                    fontSize: 12,
                    color: Colors.grey[700],
                    fontWeight: FontWeight.w500,
                  ),
                ),
              ),
            );
          }).toList(),
        ),
        const SizedBox(height: 8),
      ],
    );
  }

  List<Map<String, String>> _parseTopics(String topicsStr) {
    List<Map<String, String>> result = [];
    
    try {
      // 尝试解析为JSON
      dynamic parsed = json.decode(topicsStr);
      
      if (parsed is List) {
        // JSON数组格式: [{"id": "1", "content": "游戏"}, {"id": "2", "content": "攻略"}]
        for (var item in parsed) {
          if (item is Map<String, dynamic>) {
            result.add({
              'id': item['id']?.toString() ?? '',
              'content': item['content']?.toString() ?? '',
            });
          }
        }
      } else if (parsed is Map<String, dynamic>) {
        // 单个JSON对象格式: {"id": "1", "content": "游戏"}
        result.add({
          'id': parsed['id']?.toString() ?? '',
          'content': parsed['content']?.toString() ?? '',
        });
      }
    } catch (e) {
      // JSON解析失败，尝试作为简单字符串处理
      List<String> simpleTopics = topicsStr
          .split(RegExp(r'[,，\s]+'))
          .where((topic) => topic.trim().isNotEmpty)
          .map((topic) => topic.trim())
          .toList();
      
      for (String topic in simpleTopics) {
        result.add({
          'id': topic,
          'content': topic,
        });
      }
    }
    
    return result;
  }

  void _navigateToTopicPage(String topicIdStr) {
    final int? topicId = int.tryParse(topicIdStr);
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => TopicPage(
          topicName: topicIdStr,
          topicId: topicId,
          description: null,
          avatarUrl: null,
          viewCount: 0,
          likeCount: 0,
          threadCount: 0,
        ),
      ),
    );
  }

  Widget _buildErrorWidget() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.error_outline,
            size: 64,
            color: Colors.grey[400],
          ),
          const SizedBox(height: 16),
          Text(
            _error ?? '加载失败',
            style: TextStyle(
              fontSize: 16,
              color: Colors.grey[600],
            ),
          ),
          const SizedBox(height: 16),
          ElevatedButton(
            onPressed: () => _loadTopicData(reset: true),
            child: const Text('重试'),
          ),
        ],
      ),
    );
  }

  Widget _buildEmptyWidget() {
    return Center(
      child: Padding(
        padding: const EdgeInsets.all(32.0),
        child: Column(
          children: [
            Icon(
              Icons.forum_outlined,
              size: 64,
              color: Colors.grey[400],
            ),
            const SizedBox(height: 16),
            Text(
              '暂无相关帖子',
              style: TextStyle(
                fontSize: 16,
                color: Colors.grey[600],
              ),
            ),
          ],
        ),
      ),
    );
  }

  String _getTopicAvatarUrl() {
    return _topicData?.icon ?? widget.avatarUrl ?? '';
  }

  String _getTopicDescription() {
    return _topicData?.desc ?? widget.description ?? '';
  }

  String _getTopicContent() {
    return _topicData?.content ?? widget.topicName;
  }

  String _getTopicBackgroundUrl() {
    return _topicData?.background ?? '';
  }

  String _buildTopicStats() {
    if (_topicData?.topicData != null) {
      final stats = _topicData!.topicData!;
      return '${stats.viewCount}浏览  ${_formatCount(stats.likeCount)}赞  ${_formatCount(stats.threadCount)}帖子';
    }
    return '${_formatCount(widget.viewCount)}浏览  ${_formatCount(widget.likeCount)}赞  ${_formatCount(widget.threadCount)}帖子';
  }

  String _getBannerTitle() {
    debugPrint('TopicPage: 获取banner标题');
    debugPrint('TopicPage: _topicData = ${_topicData != null}');
    if (_topicData != null) {
      debugPrint('TopicPage: bannerTitle = "${_topicData!.bannerTitle}"');
      debugPrint('TopicPage: desc = "${_topicData!.desc}"');
    }
    
    if (_topicData?.bannerTitle.isNotEmpty == true) {
      return _topicData!.bannerTitle;
    }
    if (_topicData?.desc.isNotEmpty == true) {
      return _topicData!.desc;
    }
    return '参与社区话题活动吧报名&战统，即有机会赢取丰厚奖励好礼~';
  }

  Widget _buildBottomInfo(ForumPost post) {
    return Row(
      children: [
        Text(
          _formatTimeAgo(post.createdAt),
          style: TextStyle(fontSize: 12, color: Colors.grey[500]),
        ),
        const Spacer(),
        Icon(Icons.remove_red_eye_outlined, size: 16, color: Colors.grey[500]),
        const SizedBox(width: 4),
        Text(_formatCount(post.viewCount), style: TextStyle(fontSize: 12, color: Colors.grey[500])),
        const SizedBox(width: 12),
        Icon(Icons.mode_comment_outlined, size: 16, color: Colors.grey[500]),
        const SizedBox(width: 4),
        Text(_formatCount((post.likeReward.postCount))),
        const SizedBox(width: 12),
        Icon(Icons.favorite_outline, size: 16, color: Colors.grey[500]),
        const SizedBox(width: 4),
        Text(_formatCount((post.likeReward.likePayCount))),
      ],
    );
  }

  String _formatTimeAgo(String createdAt) {
    try {
      final DateTime postTime = DateTime.parse(createdAt);
      final DateTime now = DateTime.now();
      final Duration diff = now.difference(postTime);
      if (diff.inMinutes < 60) {
        return '${diff.inMinutes}分钟前';
      } else if (diff.inHours < 24) {
        return '${diff.inHours}小时前';
      } else {
        return '${postTime.year}-${postTime.month.toString().padLeft(2, '0')}-${postTime.day.toString().padLeft(2, '0')}';
      }
    } catch (_) {
      return '刚刚';
    }
  }

  String _formatCount(int count) {
    if (count > 10000) {
      return '${(count / 10000).toStringAsFixed(1)}w';
    } else if (count > 1000) {
      return '${(count / 1000).toStringAsFixed(1)}k';
    } else {
      return count.toString();
    }
  }

  String _stripHtmlTags(String html) {
    if (html.isEmpty) return '';
    String result = html.replaceAll(RegExp(r'<[^>]*>', multiLine: true, caseSensitive: false), '');
    result = result
        .replaceAll('&nbsp;', ' ')
        .replaceAll('&lt;', '<')
        .replaceAll('&gt;', '>')
        .replaceAll('&amp;', '&')
        .replaceAll('&quot;', '"')
        .replaceAll('&#39;', "'")
        .replaceAll('&mdash;', '—')
        .replaceAll('&ldquo;', '"')
        .replaceAll('&rdquo;', '"');
    result = result.replaceAll(RegExp(r'\s+'), ' ').trim();
    return result;
  }

  void _navigateToPostDetail(ForumPost post) {
    Navigator.push(
      context,
      MaterialPageRoute(builder: (context) => CommunityDetailPage(post: post)),
    );
  }

  void _navigateToUserProfile(ForumUser user) {
    Navigator.push(
      context,
      MaterialPageRoute(builder: (context) => UserProfilePage(user: user)),
    );
  }

}


