import 'dart:convert';

import 'package:flutter/material.dart';
import '../../model/forum_post_list.dart';
import '../../net/api/forum_service.dart';
import '../../net/config/http_base_config.dart';
import '../../utils/activity_link_handler.dart';
import 'topic_page.dart';

class CollectionPage extends StatefulWidget {
  final int collectionId;
  final String title;

  const CollectionPage({super.key, required this.collectionId, required this.title});

  @override
  State<CollectionPage> createState() => _CollectionPageState();
}

class _CollectionPageState extends State<CollectionPage> {
  final ForumService forumService = ForumService();
  List<Widget> headerWidgets = [];
  bool isLoading = true;
  String? errorMessage;

  @override
  void initState() {
    super.initState();
    _loadData();
  }

  Future<void> _loadData() async {
    try {
      setState(() {
        isLoading = true;
        errorMessage = null;
      });
      
      final resp = await forumService.getCollection(
        baseUrl: HttpBaseConfig.forumBaseUrl,
        collectionId: widget.collectionId,
        context: context,
      );

      if (resp.code == 0 && resp.data != null) {
        final widgets = _buildHeaderWidgetsFromActLabels(resp.data!);
        setState(() {
          headerWidgets = widgets;
          isLoading = false;
        });
      } else {
        setState(() {
          errorMessage = resp.message ?? '加载失败';
          isLoading = false;
        });
      }
    } catch (e) {
      setState(() {
        errorMessage = '网络错误: $e';
        isLoading = false;
      });
    }
  }

  List<Widget> _buildHeaderWidgetsFromActLabels(List<Map<String, dynamic>> labels) {
    labels.sort((a, b) => (a['sort'] ?? 0).compareTo(b['sort'] ?? 0));
    final widgets = <Widget>[];
    for (final item in labels) {
      final style = (item['style_name'] ?? '').toString();
      final title = (item['title'] ?? '').toString();
      final count = (item['count'] ?? 0) is int ? item['count'] as int : int.tryParse(item['count'].toString()) ?? 0;
      final contentStr = (item['content'] ?? '[]').toString();
      
      List<dynamic> contentList = [];
      try {
        contentList = json.decode(contentStr) as List<dynamic>;
      } catch (e) {
        print('CollectionPage: Failed to decode content: $e');
      }

      if (style == '推荐帖子列表') {
        widgets.add(_buildRecommendList(title, contentList));
      } else if (style == '活动卡片') {
        widgets.add(_buildActivityCards(title, contentList, count));
      } else if (style == '话题卡片') {
        widgets.add(_buildTopicCards(title, contentList, count));
      } else {
      }
    }
    return widgets;
  }

  Widget _sectionTitle(String title) {
    return Padding(
      padding: const EdgeInsets.fromLTRB(0, 8, 0, 8),
      child: Row(
        children: [
          Text(
            title,
            style: const TextStyle(fontSize: 16, fontWeight: FontWeight.bold, color: Colors.black87),
          ),
        ],
      ),
    );
  }

  Widget _buildRecommendList(String title, List<dynamic> items) {
    return Container(
      width: MediaQuery.of(context).size.width,
      margin: const EdgeInsets.only(bottom: 12),
      color: Colors.white,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // 只有标题不为空时才显示标题
          if (title.trim().isNotEmpty) _sectionTitle(title),
          Container(
            width: MediaQuery.of(context).size.width,
            padding: const EdgeInsets.only(left: 0,right: 12,top: 12,bottom: 2),
            color: Colors.white,
            child: Column(
              children: items.map((e) {
                final map = Map<String, dynamic>.from(e as Map);
                final header = (map['header'] ?? '').toString();
                final display = (map['display'] ?? '').toString();
                final link = (map["link"] ?? '').toString();
                final icon = (map["icon"] ?? '').toString();
                
                return Container(
                  margin: const EdgeInsets.only(bottom: 10),
                  child: GestureDetector(
                    onTap: () => ActivityLinkHandler.handleActivityClick(context, link, display),
                    child: Stack(
                      children: [
                        Row(
                          children: [
                            Container(
                              padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 2),
                              decoration: BoxDecoration(
                                color: Colors.transparent,
                                borderRadius: BorderRadius.circular(4),
                                border: Border.all(color: Colors.orange, width: 1),
                              ),
                              child: Text(
                                header,
                                style: const TextStyle(
                                  color: Colors.orange,
                                  fontSize: 10,
                                  fontWeight: FontWeight.w600,
                                ),
                              ),
                            ),
                            const SizedBox(width: 8),
                            Expanded(
                              child: Text(
                                display,
                                maxLines: 1,
                                overflow: TextOverflow.ellipsis,
                                style: const TextStyle(fontSize: 13, color: Colors.black87, fontWeight: FontWeight.w500),
                              ),
                            ),
                          ],
                        ),
                        // 右上角图标
                        if (icon.isNotEmpty)
                          Positioned(
                            top: 0,
                            right: 0,
                            child: Container(
                              width: 20,
                              height: 20,
                              decoration: BoxDecoration(
                                borderRadius: BorderRadius.circular(10),
                                boxShadow: [
                                  BoxShadow(
                                    color: Colors.black.withOpacity(0.1),
                                    blurRadius: 2,
                                    offset: const Offset(0, 1),
                                  ),
                                ],
                              ),
                              child: ClipRRect(
                                borderRadius: BorderRadius.circular(10),
                                child: Image.network(
                                  icon,
                                  width: 20,
                                  height: 20,
                                  fit: BoxFit.cover,
                                  errorBuilder: (context, error, stackTrace) {
                                    return Container(
                                      width: 20,
                                      height: 20,
                                      decoration: BoxDecoration(
                                        color: Colors.grey[300],
                                        borderRadius: BorderRadius.circular(10),
                                      ),
                                      child: const Icon(
                                        Icons.image_not_supported,
                                        size: 12,
                                        color: Colors.grey,
                                      ),
                                    );
                                  },
                                ),
                              ),
                            ),
                          ),
                      ],
                    ),
                  ),
                );
              }).toList(),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildActivityCards(String title, List<dynamic> items, int count) {
    final perRow = count.clamp(1, 5);
    final rows = <List<Map<String, dynamic>>>[];
    final parsed = items.map((e) => Map<String, dynamic>.from(e as Map)).toList();
    for (int i = 0; i < parsed.length; i += perRow) {
      rows.add(parsed.sublist(i, (i + perRow > parsed.length) ? parsed.length : i + perRow));
    }
    return Container(
      margin: const EdgeInsets.only(bottom: 12),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // 只有标题不为空时才显示标题
          if (title.trim().isNotEmpty) _sectionTitle(title),
          Column(
            children: rows.map((row) {
              return Container(
                margin: const EdgeInsets.only(bottom: 8),
                child: Row(
                  children: [
                    ...row.map((m) => Expanded(
                          child: GestureDetector(
                            onTap: () => ActivityLinkHandler.handleActivityClick(context, (m['link'] ?? '').toString(), (m['desc'] ?? '').toString()),
                            child: Column(
                              crossAxisAlignment: CrossAxisAlignment.stretch,
                              children: [
                                // 纯图片卡片
                                Container(
                                  margin: const EdgeInsets.only(right: 8),
                                  child: Stack(
                                    clipBehavior: Clip.none, // 允许子组件超出边界
                                    children: [
                                      Container(
                                        decoration: BoxDecoration(
                                          color: Colors.white,
                                          borderRadius: BorderRadius.circular(8),
                                          boxShadow: [
                                            BoxShadow(color: Colors.black.withOpacity(0.04), blurRadius: 6, offset: const Offset(0, 1)),
                                          ],
                                        ),
                                        child: ClipRRect(
                                          borderRadius: BorderRadius.circular(8),
                                          child: Image.network(
                                            (m['cover'] ?? '').toString(),
                                            fit: BoxFit.fitWidth,
                                            width: double.infinity,
                                            errorBuilder: (c, e, s) => Container(
                                              height: 120,
                                              color: Colors.grey[200],
                                              child: const Icon(Icons.image_not_supported, color: Colors.grey),
                                            ),
                                          ),
                                        ),
                                      ),
                                      // 右上角图标
                                      if ((m['icon'] ?? '').toString().isNotEmpty)
                                        Positioned(
                                          top: -4,
                                          right: -4,
                                          child: Container(
                                            width: 20,
                                            height: 20,
                                            decoration: BoxDecoration(
                                              borderRadius: BorderRadius.circular(10),
                                              boxShadow: [
                                                BoxShadow(
                                                  color: Colors.black.withOpacity(0.2),
                                                  blurRadius: 3,
                                                  offset: const Offset(0, 1),
                                                ),
                                              ],
                                            ),
                                            child: ClipRRect(
                                              borderRadius: BorderRadius.circular(10),
                                              child: Image.network(
                                                (m['icon'] ?? '').toString(),
                                                width: 20,
                                                height: 20,
                                                fit: BoxFit.cover,
                                                errorBuilder: (context, error, stackTrace) {
                                                  return Container(
                                                    width: 20,
                                                    height: 20,
                                                    decoration: BoxDecoration(
                                                      color: Colors.grey[300],
                                                      borderRadius: BorderRadius.circular(10),
                                                    ),
                                                    child: const Icon(
                                                      Icons.image_not_supported,
                                                      size: 12,
                                                      color: Colors.grey,
                                                    ),
                                                  );
                                                },
                                              ),
                                            ),
                                          ),
                                        ),
                                    ],
                                  ),
                                ),
                                // 文本独立在图片下方（只有有内容时才显示）
                                if ((m['desc'] ?? '').toString().trim().isNotEmpty) ...[
                                  const SizedBox(height: 6),
                                  Text(
                                    (m['desc'] ?? '').toString(),
                                    maxLines: 1,
                                    overflow: TextOverflow.ellipsis,
                                    textAlign: TextAlign.left,
                                    style: const TextStyle(fontSize: 14, fontWeight: FontWeight.w500),
                                  ),
                                ],
                              ],
                            ),
                          ),
                        )),
                    // 填充空位
                    ...List.generate(perRow - row.length, (i) => const Expanded(child: SizedBox.shrink())),
                  ],
                ),
              );
            }).toList(),
          ),
        ],
      ),
    );
  }

  Widget _buildTopicCards(String title, List<dynamic> items, int count) {
    final perRow = count.clamp(1, 5);
    final parsed = items.map((e) => Map<String, dynamic>.from(e as Map)).toList();
    final rows = <List<Map<String, dynamic>>>[];
    for (int i = 0; i < parsed.length; i += perRow) {
      rows.add(parsed.sublist(i, (i + perRow > parsed.length) ? parsed.length : i + perRow));
    }
    return Container(
      margin: const EdgeInsets.only(bottom: 12),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // 只有标题不为空时才显示标题
          if (title.trim().isNotEmpty) _sectionTitle(title),
          Column(
            children: rows.map((row) {
                return Container(
                margin: const EdgeInsets.only(bottom: 0),
                child: Row(
                  children: [
                    ...row.map((m) => Expanded(
                          child: GestureDetector(
                            onTap: () {
                              final Map<String, dynamic> data = m;
                              String topicName = (data['topicName'] ?? data['topic_name'] ?? '').toString();
                              int? topicId = int.tryParse((data['topicId'] ?? data['topic_id'] ?? '').toString());
                              String? desc;
                              final dynamic d = data['topicDesc'] ?? data['desc'] ?? data['description'];
                              if (d != null) desc = d.toString();
                              String? avatar;
                              final dynamic a = data['cover'] ?? data['avatar'] ?? data['icon'];
                              if (a != null && a.toString().isNotEmpty) avatar = a.toString();

                              int parseInt(dynamic v) {
                                if (v is int) return v;
                                if (v == null) return 0;
                                return int.tryParse(v.toString()) ?? 0;
                              }

                              int threadCount = parseInt(data['threadCount'] ?? data['thread_count']);
                              int viewCount = parseInt(data['view_count'] ?? data['viewCount']);
                              int likeCount = parseInt(data['like_count'] ?? data['likeCount']);
                              if (data['topicData'] is Map) {
                                final td = Map<String, dynamic>.from(data['topicData'] as Map);
                                threadCount = threadCount == 0 ? parseInt(td['threadCount'] ?? td['thread_count']) : threadCount;
                                viewCount = viewCount == 0 ? parseInt(td['view_count'] ?? td['viewCount']) : viewCount;
                                likeCount = likeCount == 0 ? parseInt(td['like_count'] ?? td['likeCount']) : likeCount;
                              }

                              Navigator.push(
                                context,
                                MaterialPageRoute(
                                  builder: (context) => TopicPage(
                                    topicName: topicName.isEmpty ? '话题' : topicName,
                                    topicId: topicId,
                                    description: desc,
                                    avatarUrl: avatar,
                                    viewCount: viewCount,
                                    likeCount: likeCount,
                                    threadCount: threadCount,
                                  ),
                                ),
                              );
                            },
                                                            child: Container(
                                margin: const EdgeInsets.only(right: 4),
                                height: 60,
                                decoration: const BoxDecoration(
                                  color: Colors.white,
                                ),
                                child: Padding(
                                  padding: const EdgeInsets.only(left:0,top: 0,right: 6,bottom: 0),
                                  child: Row(
                                    children: [
                                      if ((m['cover'] ?? '').toString().isNotEmpty)
                                        Container(
                                          width: 44, // 增加容器宽度，为icon角标留出空间
                                          height: 44, // 增加容器高度，为icon角标留出空间
                                          child: Stack(
                                            clipBehavior: Clip.none, // 允许子组件超出边界
                                            children: [
                                              Positioned(
                                                left: 0,
                                                top: 0,
                                                child: ClipRRect(
                                                  borderRadius: BorderRadius.circular(6),
                                                  child: Image.network((m['cover'] ?? '').toString(), width: 40, height: 40, fit: BoxFit.cover,
                                                      errorBuilder: (c, e, s) => Container(width: 40, height: 40, color: Colors.grey[200])),
                                                ),
                                              ),
                                              // cover图标的右上角图标
                                              if ((m['icon'] ?? '').toString().isNotEmpty)
                                                Positioned(
                                                  top: -2,
                                                  right: -2,
                                                  child: Container(
                                                    width: 12,
                                                    height: 12,
                                                    decoration: BoxDecoration(
                                                      borderRadius: BorderRadius.circular(8),
                                                      boxShadow: [
                                                        BoxShadow(
                                                          color: Colors.black.withOpacity(0.2),
                                                          blurRadius: 2,
                                                          offset: const Offset(0, 1),
                                                        ),
                                                      ],
                                                    ),
                                                    child: ClipRRect(
                                                      borderRadius: BorderRadius.circular(8),
                                                      child: Image.network(
                                                        (m['icon'] ?? '').toString(),
                                                        width: 16,
                                                        height: 16,
                                                        fit: BoxFit.cover,
                                                        errorBuilder: (context, error, stackTrace) {
                                                          return Container(
                                                            width: 16,
                                                            height: 16,
                                                            decoration: BoxDecoration(
                                                              color: Colors.grey[300],
                                                              borderRadius: BorderRadius.circular(8),
                                                            ),
                                                            child: const Icon(
                                                              Icons.image_not_supported,
                                                              size: 8,
                                                              color: Colors.grey,
                                                            ),
                                                          );
                                                        },
                                                      ),
                                                    ),
                                                  ),
                                                ),
                                            ],
                                          ),
                                        ),
                                      const SizedBox(width: 0),
                                      Expanded(
                                        child: Column(
                                          crossAxisAlignment: CrossAxisAlignment.start,
                                          mainAxisAlignment: MainAxisAlignment.center,
                                          children: [
                                            Text(
                                              '#${(m['topicName'] ?? '').toString()}',
                                              maxLines: 1,
                                              overflow: TextOverflow.ellipsis,
                                              style: const TextStyle(fontSize: 13, fontWeight: FontWeight.w600),
                                            ),
                                            Padding(
                                              padding: const EdgeInsets.only(top: 2),
                                              child: Text(
                                                _topicStatsText(m),
                                                maxLines: 2,
                                                overflow: TextOverflow.ellipsis,
                                                style: TextStyle(fontSize: 9, color: Colors.grey[600]),
                                              ),
                                            ),
                                          ],
                                        ),
                                      ),
                                    ],
                                  ),
                                ),
                                ),
                          ),
                        )),
                    ...List.generate(perRow - row.length, (i) => const Expanded(child: SizedBox.shrink())),
                  ],
                ),
              );
            }).toList(),
          ),
        ],
      ),
    );
  }

  String _topicStatsText(Map<String, dynamic> item) {
    int? readInt(Map<String, dynamic> map, List<String> keys) {
      for (final key in keys) {
        final dynamic value = map[key];
        if (value is int) return value;
        if (value != null) {
          final parsed = int.tryParse(value.toString());
          if (parsed != null) return parsed;
        }
      }
      return null;
    }

    String? readStr(Map<String, dynamic> map, List<String> keys) {
      for (final key in keys) {
        final dynamic value = map[key];
        if (value != null) {
          final s = value.toString();
          if (s.isNotEmpty) return s;
        }
      }
      return null;
    }

    // 顶层优先
    int? threadCount = readInt(item, ['threadCount', 'thread_count']);
    String? viewCount = readStr(item, ['view_count', 'viewCount']);
    String? likeCount = readStr(item, ['like_count', 'likeCount']);

    // 回退 topicData（不再依赖 showTopicData 的取值，只要有数据就用）
    if (item['topicData'] is Map) {
      final data = Map<String, dynamic>.from(item['topicData'] as Map);
      threadCount ??= readInt(data, ['threadCount', 'thread_count']);
      viewCount ??= readStr(data, ['view_count', 'viewCount']);
      likeCount ??= readStr(data, ['like_count', 'likeCount']);
    }

    // 兜底
    threadCount ??= 0;
    viewCount ??= '0';
    likeCount ??= '0';

    return '${viewCount}浏览 · ${likeCount}点赞 · ${threadCount}帖';
  }







  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.white,
      appBar: AppBar(
        title: Text(widget.title),
        backgroundColor: Colors.white,
        foregroundColor: Colors.black,
        elevation: 1,
      ),
      body: RefreshIndicator(
        onRefresh: _loadData,
        child: isLoading
            ? const Center(child: CircularProgressIndicator())
            : errorMessage != null
                ? Center(
                    child: Column(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        Text(
                          errorMessage!,
                          style: const TextStyle(color: Colors.red),
                        ),
                        const SizedBox(height: 16),
                        ElevatedButton(
                          onPressed: _loadData,
                          child: const Text('重试'),
                        ),
                      ],
                    ),
                  )
                : headerWidgets.isEmpty
                    ? const Center(child: Text('暂无内容'))
                    : SingleChildScrollView(
                        padding: const EdgeInsets.all(12),
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: headerWidgets,
                        ),
                      ),
      ),
    );
  }
}
