import 'package:easy_refresh/easy_refresh.dart';
import 'package:flutter/material.dart';
import '../../components/refresh_header.dart';
import '../../net/api/forum_service.dart';
import '../../net/config/http_base_config.dart';
import '../../model/forum_category.dart';
import '../../model/forum_post_list.dart';
import '../community/community_detail_page.dart';
import '../../components/video_player_card.dart';

class ForumPostWaterfallListPage extends StatefulWidget {
  final ForumCategory category;
  final List<Widget>? headerWidgets;
  
  const ForumPostWaterfallListPage({
    Key? key,
    required this.category,
    this.headerWidgets,
  }) : super(key: key);

  @override
  State<ForumPostWaterfallListPage> createState() => _ForumPostWaterfallListPageState();
}

class _ForumPostWaterfallListPageState extends State<ForumPostWaterfallListPage>
    with AutomaticKeepAliveClientMixin {
  final ForumService _forumService = ForumService();
  List<ForumPost> _posts = [];
  bool _isLoading = true;
  bool _hasError = false;
  String? _errorMessage;
  final ScrollController _scrollController = ScrollController();
  bool _isLoadingMore = false;
  int _currentPage = 1;
  int _totalPage = 1;
  bool _hasMore = true;

  @override
  bool get wantKeepAlive => true;

  @override
  void initState() {
    super.initState();
    _loadPosts(reset: true);
    _scrollController.addListener(_handleScroll);
  }

  void _handleScroll() {
    if (!_hasMore || _isLoadingMore || _isLoading) return;
    if (!_scrollController.hasClients) return;
    final position = _scrollController.position;
    if (position.extentAfter < 300) {
      _loadPosts();
    }
  }

  Future<void> _loadPosts({bool reset = false}) async {
    try {
      if (reset) {
        setState(() {
          _isLoading = true;
          _hasError = false;
          _currentPage = 1;
          _hasMore = true;
        });
      } else {
        if (!_hasMore) return;
        setState(() {
          _isLoadingMore = true;
        });
      }

      final response = await _forumService.getPostList(
        baseUrl: HttpBaseConfig.forumBaseUrl,
        categoryId: widget.category.categoryId,
        categoryName: widget.category.name,
        page: reset ? 1 : (_currentPage + 1),
        stype: widget.category.stype,
        pageSize: 20,
        context: context,
      );

      if (response.code == 0 && response.data != null) {
        final data = response.data!;
        setState(() {
          if (reset) {
            _posts
              ..clear()
              ..addAll(data.pageData);
          } else {
            _posts.addAll(data.pageData);
          }
          _currentPage = data.currentPage;
          _totalPage = data.totalPage;
          _hasMore = _currentPage < _totalPage && data.pageData.isNotEmpty;
          _isLoading = false;
          _isLoadingMore = false;
        });
      } else {
        setState(() {
          _hasError = true;
          _errorMessage = response.message ?? '加载失败';
          _isLoading = false;
          _isLoadingMore = false;
        });
      }
    } catch (e) {
      setState(() {
        _hasError = true;
        _errorMessage = '网络错误: $e';
        _isLoading = false;
        _isLoadingMore = false;
      });
    }
  }

  @override
  void dispose() {
    _scrollController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    super.build(context); // 必须调用 super.build() 用于 AutomaticKeepAliveClientMixin
    
    EasyRefreshController controller = EasyRefreshController(
        controlFinishRefresh: true,
        controlFinishLoad: true
    );
    
    return Container(
      color: Colors.white,
      margin: const EdgeInsets.only(top: 64),
      child: EasyRefresh(
          controller: controller,
          header: const CustomerHeader(),
          onRefresh: () async {
            await _loadPosts(reset: true);
            controller.finishRefresh();
            controller.resetFooter();
          },
          child: CustomScrollView(
            physics: const AlwaysScrollableScrollPhysics(),
            controller: _scrollController,
            slivers: [
              if ((widget.headerWidgets ?? const []).isNotEmpty)
                SliverToBoxAdapter(
                  child: Padding(
                    padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
                    child: Column(children: widget.headerWidgets!),
                  ),
                ),

              // 状态占位：加载/错误/空
              if (_isLoading)
                const SliverFillRemaining(
                  hasScrollBody: false,
                  child: Center(child: CircularProgressIndicator()),
                )
              else if (_hasError)
                SliverFillRemaining(
                  hasScrollBody: false,
                  child: Center(
                    child: Column(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        Icon(
                          Icons.error_outline,
                          size: 64,
                          color: Colors.grey, // 简化色值以避免上下文依赖
                        ),
                        SizedBox(height: 16),
                        Text(
                          _errorMessage ?? '加载失败',
                          style: TextStyle(fontSize: 16, color: Colors.grey),
                        ),
                        SizedBox(height: 16),
                        ElevatedButton(
                          onPressed: _loadPosts,
                          child: Text('重试'),
                        ),
                      ],
                    ),
                  ),
                )
              else if (_posts.isEmpty)
                  const SliverFillRemaining(
                    hasScrollBody: false,
                    child: Center(
                      child: Text(
                        '暂无帖子',
                        style: TextStyle(fontSize: 16, color: Colors.grey),
                      ),
                    ),
                  )
                else ...[
                    SliverPadding(
                      padding: const EdgeInsets.all(16),
                      sliver: SliverToBoxAdapter(
                        child: PostWaterfallFlow(
                          posts: _posts,
                          onPostTap: (post) {
                            Navigator.push(
                              context,
                              MaterialPageRoute(
                                builder: (context) => CommunityDetailPage(post: post),
                              ),
                            );
                          },
                        ),
                      ),
                    ),
                    SliverToBoxAdapter(
                      child: Padding(
                        padding: const EdgeInsets.only(bottom: 16),
                        child: Center(
                          child: _isLoadingMore
                              ? const Padding(
                            padding: EdgeInsets.symmetric(vertical: 12),
                            child: SizedBox(
                              height: 24,
                              width: 24,
                              child: CircularProgressIndicator(strokeWidth: 2),
                            ),
                          )
                              : (_hasMore
                              ? const SizedBox.shrink()
                              : Text(
                            '没有更多了',
                            style: TextStyle(color: Colors.grey, fontSize: 12),
                          )),
                        ),
                      ),
                    ),
                  ],
            ],
          )
      ),
    );
  }
}

class PostWaterfallFlow extends StatelessWidget {
  final List<ForumPost> posts;
  final Function(ForumPost)? onPostTap;

  const PostWaterfallFlow({
    Key? key,
    required this.posts,
    this.onPostTap,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    // 过滤出有图片或视频的帖子
    final postsWithMedia = posts.where((post) => _hasMedia(post)).toList();
    
    if (postsWithMedia.isEmpty) {
      return const Center(
        child: Text(
          '暂无图片/视频内容',
          style: TextStyle(
            fontSize: 16,
            color: Colors.grey,
          ),
        ),
      );
    }

    // 将帖子分为两列
    final leftColumn = <ForumPost>[];
    final rightColumn = <ForumPost>[];
    
    double leftHeight = 0;
    double rightHeight = 0;
    
    // 动态分配到高度较短的列
    for (final post in postsWithMedia) {
      final cardHeight = _estimateCardHeight(post);
      
      if (leftHeight <= rightHeight) {
        leftColumn.add(post);
        leftHeight += cardHeight + 12; // 加上间距
      } else {
        rightColumn.add(post);
        rightHeight += cardHeight + 12; // 加上间距
      }
    }

    return Row(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // 左列
        Expanded(
          child: Column(
            children: leftColumn.map((post) => 
              Padding(
                padding: const EdgeInsets.only(bottom: 12, right: 6),
                child: _buildPostCard(context, post),
              )
            ).toList(),
          ),
        ),
        
        // 右列
        Expanded(
          child: Column(
            children: rightColumn.map((post) => 
              Padding(
                padding: const EdgeInsets.only(bottom: 12, left: 6),
                child: _buildPostCard(context, post),
              )
            ).toList(),
          ),
        ),
      ],
    );
  }

  Widget _buildPostCard(BuildContext context, ForumPost post) {
    final mediaHeight = _estimateMediaHeight(post);
    
    return Container(
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.1),
            blurRadius: 6,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: ClipRRect(
        borderRadius: BorderRadius.circular(12),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // 媒体内容区域（图片/视频）
            Container(
              width: double.infinity,
              height: mediaHeight,
              color: Colors.grey[200],
              child: _buildMediaContent(context, post),
            ),
              
            // 标题和信息区域（白色背景，顶部紧贴媒体）
            GestureDetector(
              onTap: () => onPostTap?.call(post),
              child: Container(
                width: double.infinity,
                color: Colors.white,
                padding: const EdgeInsets.fromLTRB(12, 8, 12, 12),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    // 帖子标题
                    Text(
                      post.title,
                      style: const TextStyle(
                        fontSize: 14,
                        fontWeight: FontWeight.bold,
                        color: Colors.black87,
                        height: 1.2,
                      ),
                      maxLines: 2,
                      overflow: TextOverflow.ellipsis,
                    ),
                    
                    const SizedBox(height: 8),
                    
                    // 底部信息：作者和浏览量
                    Row(
                      children: [
                        // 作者头像
                        Container(
                          width: 16,
                          height: 16,
                          decoration: BoxDecoration(
                            color: Colors.grey[300],
                            borderRadius: BorderRadius.circular(8),
                          ),
                          child: ClipRRect(
                            borderRadius: BorderRadius.circular(8),
                            child: post.user.avatar.isNotEmpty
                                ? Image.network(
                                    post.user.avatar,
                                    fit: BoxFit.cover,
                                    errorBuilder: (context, error, stackTrace) {
                                      return const Icon(
                                        Icons.person,
                                        size: 12,
                                        color: Colors.grey,
                                      );
                                    },
                                  )
                                : const Icon(
                                    Icons.person,
                                    size: 12,
                                    color: Colors.grey,
                                  ),
                          ),
                        ),
                        const SizedBox(width: 4),
                        Expanded(
                          child: Text(
                            post.user.nickname,
                            style: const TextStyle(
                              fontSize: 10,
                              color: Colors.grey,
                            ),
                            overflow: TextOverflow.ellipsis,
                          ),
                        ),
                        const Icon(
                          Icons.visibility,
                          size: 12,
                          color: Colors.grey,
                        ),
                        const SizedBox(width: 2),
                        Text(
                          '${post.viewCount}',
                          style: const TextStyle(
                            fontSize: 10,
                            color: Colors.grey,
                          ),
                        ),
                      ],
                    ),
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildMediaContent(BuildContext context, ForumPost post) {
    // 优先显示图片
    if (_hasImage(post)) {
      // 获取所有图片（包括content.images和图文分离的图片）
      List<dynamic> allImages = _getAllImages(post);
      if (allImages.isNotEmpty) {
        final firstImage = allImages.first;
        String? imageUrl;
        
        // 尝试从不同字段获取图片URL，参考forum_post_list_page.dart的逻辑
        if (firstImage is Map<String, dynamic>) {
          imageUrl = firstImage['url'] ?? firstImage['thumbUrl'] ?? firstImage['attachment'];
        } else if (firstImage is String) {
          imageUrl = firstImage;
        }
        
        if (imageUrl != null && imageUrl.isNotEmpty) {
          return GestureDetector(
            onTap: () => _showImageGallery(context, allImages, 0),
            child: Image.network(
              imageUrl,
              fit: BoxFit.cover,
              width: double.infinity,
              height: double.infinity,
              errorBuilder: (context, error, stackTrace) {
                return Container(
                  color: Colors.grey[300],
                  child: const Center(
                    child: Icon(
                      Icons.image_not_supported,
                      color: Colors.grey,
                      size: 32,
                    ),
                  ),
                );
              },
              loadingBuilder: (context, child, loadingProgress) {
                if (loadingProgress == null) return child;
                return Container(
                  color: Colors.grey[200],
                  child: const Center(
                    child: CircularProgressIndicator(),
                  ),
                );
              },
            ),
          );
        }
      }
    }
    
    // 显示视频（全屏播放）
    if (_hasVideo(post)) {
      String? videoUrl;
      String? coverUrl;
      final indexes = post.content.indexes;
      if (indexes.containsKey('108')) {
        final videoIndex = indexes['108'];
        if (videoIndex is Map<String, dynamic> && videoIndex['body'] is List) {
          final bodyList = videoIndex['body'] as List;
          if (bodyList.isNotEmpty && bodyList.first is Map<String, dynamic>) {
            final videoData = bodyList.first as Map<String, dynamic>;
            videoUrl = videoData['url'];
            if (videoData['cover'] is Map<String, dynamic>) {
              coverUrl = videoData['cover']['url'];
            } else {
              coverUrl = videoData['thumbUrl'];
            }
          }
        }
      }
      if (videoUrl != null && videoUrl.isNotEmpty) {
        return GestureDetector(
          onTap: () => _playVideoFullscreen(context, videoUrl!),
          child: _buildVideoThumbnailFromIndex(context, post),
        );
      }
      return _buildVideoThumbnailFromIndex(context, post);
    }
    
    // 没有媒体内容时的占位符
    return Container(
      color: Colors.grey[300],
      child: const Center(
        child: Icon(
          Icons.image_not_supported,
          color: Colors.grey,
          size: 32,
        ),
      ),
    );
  }

  String? _getFirstVideoUrl(ForumPost post) {
    final indexes = post.content.indexes;
    if (!indexes.containsKey('108')) return null;
    final videoIndex = indexes['108'];
    if (videoIndex is Map<String, dynamic> && videoIndex['body'] is List) {
      final bodyList = videoIndex['body'] as List;
      if (bodyList.isNotEmpty && bodyList.first is Map<String, dynamic>) {
        final map = bodyList.first as Map<String, dynamic>;
        final String? url = map['url'];
        return url;
      }
    }
    return null;
  }

  // 估算媒体内容高度
  double _estimateMediaHeight(ForumPost post) {
    // 基础媒体高度
    double baseHeight = 150;
    
    // 添加一些随机性以创建瀑布流效果
    final randomFactor = (post.threadId % 5) * 0.2 + 0.8; // 0.8 to 1.6
    
    return baseHeight * randomFactor;
  }

  // 估算整个卡片高度（用于瀑布流布局计算）
  double _estimateCardHeight(ForumPost post) {
    // 媒体内容高度
    double mediaHeight = _estimateMediaHeight(post);
    
    // 标题区域固定高度（大约）
    double titleAreaHeight = 60;
    
    // 根据标题长度稍作调整
    final titleLength = post.title.length;
    if (titleLength > 30) {
      titleAreaHeight += 10; // 长标题稍微增加高度
    }
    
    return mediaHeight + titleAreaHeight;
  }

  // 检查是否有媒体内容
  bool _hasMedia(ForumPost post) {
    return _hasImage(post) || _hasVideo(post);
  }

  // 检查是否有图片
  bool _hasImage(ForumPost post) {
    // 检查content.images
    if (post.content.images.isNotEmpty) {
      return true;
    }
    
    // 检查图文分离的图片（indexes[101]）
    final bool isSeparatedContent = post.from != 2 && post.isMixThread != 1;
    if (isSeparatedContent) {
      final indexes = post.content.indexes;
      if (indexes.containsKey('101')) {
        final imageIndex = indexes['101'];
        if (imageIndex is Map<String, dynamic> && imageIndex['body'] is List) {
          final bodyList = imageIndex['body'] as List;
          for (var item in bodyList) {
            if (item is Map<String, dynamic>) {
              final imageUrl = item['url'] ?? item['thumbUrl'] ?? item['attachment'];
              if (imageUrl != null && imageUrl.isNotEmpty) {
                return true;
              }
            }
          }
        }
      }
    }
    
    return false;
  }

  // 检查是否有视频
  bool _hasVideo(ForumPost post) {
    final indexes = post.content.indexes;
    if (indexes.containsKey('108')) {
      final videoIndex = indexes['108'];
      if (videoIndex is Map<String, dynamic> && videoIndex['body'] is List) {
        final bodyList = videoIndex['body'] as List;
        return bodyList.isNotEmpty;
      }
    }
    return false;
  }

  /// 获取帖子中所有图片（包括content.images和图文分离的图片）
  List<dynamic> _getAllImages(ForumPost post) {
    List<dynamic> allImages = [];
    
    // 先添加content.images中的图片
    if (post.content.images.isNotEmpty) {
      allImages.addAll(post.content.images);
    }
    
    // 判断是否是图文分离的帖子
    final bool isSeparatedContent = post.from != 2 && post.isMixThread != 1;
    
    if (isSeparatedContent) {
      // 从indexes[101]获取图文分离的图片
      final indexes = post.content.indexes;
      
      if (indexes.containsKey('101')) {
        final imageIndex = indexes['101'];
        
        if (imageIndex is Map<String, dynamic> && imageIndex['body'] is List) {
          final bodyList = imageIndex['body'] as List;
          
          for (var item in bodyList) {
            if (item is Map<String, dynamic>) {
              final imageUrl = item['url'] ?? item['thumbUrl'] ?? item['attachment'];
              if (imageUrl != null && imageUrl.isNotEmpty) {
                allImages.add(item);
              }
            }
          }
        }
      }
    }
    
    return allImages;
  }

  /// 从索引108构建视频缩略图
  Widget _buildVideoThumbnailFromIndex(BuildContext context, ForumPost post) {
    final indexes = post.content.indexes;
    if (!indexes.containsKey('108')) {
      return Container(
        color: Colors.grey[800],
        child: const Center(
          child: Icon(
            Icons.videocam,
            color: Colors.white,
            size: 48,
          ),
        ),
      );
    }

    final videoIndex = indexes['108'];
    if (videoIndex is! Map<String, dynamic> || videoIndex['body'] is! List) {
      return Container(
        color: Colors.grey[800],
        child: const Center(
          child: Icon(
            Icons.videocam,
            color: Colors.white,
            size: 48,
          ),
        ),
      );
    }

    final bodyList = videoIndex['body'] as List;
    if (bodyList.isEmpty) {
      return Container(
        color: Colors.grey[800],
        child: const Center(
          child: Icon(
            Icons.videocam,
            color: Colors.white,
            size: 48,
          ),
        ),
      );
    }

    final videoData = bodyList.first;
    if (videoData is! Map<String, dynamic>) {
      return Container(
        color: Colors.grey[800],
        child: const Center(
          child: Icon(
            Icons.videocam,
            color: Colors.white,
            size: 48,
          ),
        ),
      );
    }

    final String? thumbUrl = videoData['thumbUrl'];
    
    // 从cover字段获取视频预览图
    String? coverUrl;
    if (videoData['cover'] is Map<String, dynamic>) {
      coverUrl = videoData['cover']['url'];
    }
    
    return Container(
      width: double.infinity,
      height: double.infinity,
      decoration: BoxDecoration(
        color: Colors.grey[300],
        borderRadius: BorderRadius.circular(12),
      ),
      child: Stack(
        alignment: Alignment.center,
        children: [
          // 视频缩略图
          Container(
            width: double.infinity,
            height: double.infinity,
            decoration: BoxDecoration(
              color: Colors.grey[800],
              borderRadius: BorderRadius.circular(12),
            ),
            child: (coverUrl != null && coverUrl.isNotEmpty)
                ? ClipRRect(
                    borderRadius: BorderRadius.circular(12),
                    child: Image.network(
                      coverUrl,
                      width: double.infinity,
                      height: double.infinity,
                      fit: BoxFit.cover,
                      errorBuilder: (context, error, stackTrace) {
                        return Container(
                          color: Colors.grey[800],
                          child: const Center(
                            child: Icon(
                              Icons.videocam,
                              color: Colors.white,
                              size: 48,
                            ),
                          ),
                        );
                      },
                    ),
                  )
                : (thumbUrl != null && thumbUrl.isNotEmpty)
                    ? ClipRRect(
                        borderRadius: BorderRadius.circular(12),
                        child: Image.network(
                          thumbUrl,
                          width: double.infinity,
                          height: double.infinity,
                          fit: BoxFit.cover,
                          errorBuilder: (context, error, stackTrace) {
                            return Container(
                              color: Colors.grey[800],
                              child: const Center(
                                child: Icon(
                                  Icons.videocam,
                                  color: Colors.white,
                                  size: 48,
                                ),
                              ),
                            );
                          },
                        ),
                      )
                    : Icon(
                        Icons.videocam,
                        size: 48,
                        color: Colors.white,
                      ),
          ),
          
          // 播放按钮
          Container(
            width: 40,
            height: 40,
            decoration: const BoxDecoration(
              color: Colors.black54,
              shape: BoxShape.circle,
            ),
            child: const Icon(
              Icons.play_arrow,
              color: Colors.white,
              size: 24,
            ),
          ),
        ],
      ),
    );
  }

  /// 全屏播放视频
  void _playVideoFullscreen(BuildContext context, String videoUrl) {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => VideoPlayerFullScreenPage(videoUrl: videoUrl),
      ),
    );
  }

  /// 显示图片画廊（支持左右滑动）
  void _showImageGallery(BuildContext context, List<dynamic> images, int initialIndex) {
    showDialog(
      context: context,
      barrierColor: Colors.black87,
      builder: (BuildContext context) {
        return _ImageGalleryDialog(images: images, initialIndex: initialIndex);
      },
    );
  }
}

/// 图片画廊弹窗组件
class _ImageGalleryDialog extends StatefulWidget {
  final List<dynamic> images;
  final int initialIndex;

  const _ImageGalleryDialog({
    required this.images,
    required this.initialIndex,
  });

  @override
  State<_ImageGalleryDialog> createState() => _ImageGalleryDialogState();
}

class _ImageGalleryDialogState extends State<_ImageGalleryDialog> {
  late PageController _pageController;
  late int _currentIndex;

  @override
  void initState() {
    super.initState();
    _currentIndex = widget.initialIndex;
    _pageController = PageController(initialPage: widget.initialIndex);
  }

  @override
  void dispose() {
    _pageController.dispose();
    super.dispose();
  }

  String? _getImageUrl(dynamic imageData) {
    if (imageData is Map<String, dynamic>) {
      return imageData['url'] ?? imageData['thumbUrl'] ?? imageData['attachment'];
    } else if (imageData is String) {
      return imageData;
    }
    return null;
  }

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: () => Navigator.of(context).pop(),
      child: Dialog.fullscreen(
        backgroundColor: Colors.transparent,
        child: Stack(
          children: [
            // 背景可点击区域
            Container(
              width: double.infinity,
              height: double.infinity,
              color: Colors.transparent,
            ),
            // 图片轮播
            PageView.builder(
              controller: _pageController,
              onPageChanged: (index) {
                setState(() {
                  _currentIndex = index;
                });
              },
              itemCount: widget.images.length,
              itemBuilder: (context, index) {
                final imageUrl = _getImageUrl(widget.images[index]);
                if (imageUrl == null || imageUrl.isEmpty) {
                  return const Center(
                    child: Icon(
                      Icons.broken_image,
                      size: 64,
                      color: Colors.white54,
                    ),
                  );
                }

                return Center(
                  child: InteractiveViewer(
                    panEnabled: true,
                    scaleEnabled: true,
                    minScale: 0.5,
                    maxScale: 3.0,
                    child: Image.network(
                      imageUrl,
                      fit: BoxFit.contain,
                      loadingBuilder: (context, child, loadingProgress) {
                        if (loadingProgress == null) return child;
                        return const Center(
                          child: CircularProgressIndicator(
                            color: Colors.white,
                          ),
                        );
                      },
                      errorBuilder: (context, error, stackTrace) => const Center(
                        child: Icon(
                          Icons.broken_image,
                          size: 64,
                          color: Colors.white54,
                        ),
                      ),
                    ),
                  ),
                );
              },
            ),
            // 页面指示器
            if (widget.images.length > 1)
              Positioned(
                bottom: 100,
                left: 0,
                right: 0,
                child: Center(
                  child: Container(
                    padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
                    decoration: BoxDecoration(
                      color: Colors.black54,
                      borderRadius: BorderRadius.circular(20),
                    ),
                    child: Text(
                      '${_currentIndex + 1} / ${widget.images.length}',
                      style: const TextStyle(
                        color: Colors.white,
                        fontSize: 14,
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                  ),
                ),
              ),
            // 关闭按钮
            Positioned(
              top: 50,
              right: 20,
              child: GestureDetector(
                onTap: () => Navigator.of(context).pop(),
                child: Container(
                  padding: const EdgeInsets.all(8),
                  decoration: const BoxDecoration(
                    color: Colors.black54,
                    shape: BoxShape.circle,
                  ),
                  child: const Icon(
                    Icons.close,
                    color: Colors.white,
                    size: 24,
                  ),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}