import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'dart:convert';
import 'dart:io';
import '../../providers/game_circle_provider.dart';
import '../../providers/game_circle_config_provider.dart';
import '../../providers/user_provider.dart';
import '../../config/app_config.dart';
import '../../manager/channel_manager.dart';
import '../../model/game_binding_info.dart';
import '../../services/bind_account_service.dart';
import '../../model/package_info.dart';
import '../../utils/log_util.dart';

class OpenGames extends StatefulWidget {
  const OpenGames({super.key});

  @override
  State<OpenGames> createState() => _OpenGamesState();
}

class _OpenGamesState extends State<OpenGames> {
  List<PackageInfoItem> _packageList = [];
  bool _isLoading = true;
  String? _error;

  @override
  void initState() {
    super.initState();
    _loadGamePackages();
  }

  /// Load game packages
  Future<void> _loadGamePackages() async {
    try {
      setState(() {
        _isLoading = true;
        _error = null;
      });

      final gameCircleProvider = Provider.of<GameCircleProvider>(
        context,
        listen: false,
      );

      if (!gameCircleProvider.hasSelectedGameCircle ||
          gameCircleProvider.selectedGameCircle == null) {
        throw Exception('No selected game circle');
      }

      final selectedCircle = gameCircleProvider.selectedGameCircle!;
      final tgid = selectedCircle.tgid;

      LogUtil.d('Starting to get game package info, tgid: $tgid');

      // Call bind account service API to get package info
      final bindAccountService = BindAccountService();
      final response = await bindAccountService.getPackageInfo(
        pid: AppConfig.pid,
        gid: AppConfig.gid,
        tgid: tgid,
        os: Platform.isAndroid ? 'android' : 'ios',
        packageNameList: [], // Empty list means get all available packages
      );

      if (response.success && response.data?.data?.packageInfo != null) {
        final packages = response.data!.data!.packageInfo;
        LogUtil.d('Game package info retrieved successfully, ${packages.length} packages');
        
        if (mounted) {
          setState(() {
            _packageList = packages;
            _isLoading = false;
          });
        }
      } else {
        LogUtil.w('Failed to get game package info: ${response.message}');
        throw Exception(response.message);
      }
    } catch (e) {
      LogUtil.e('Exception loading game package info: $e');
      if (mounted) {
        setState(() {
          _error = e.toString();
          _isLoading = false;
        });
      }
    }
  }

  /// Launch game binding
  Future<void> _launchGame(PackageInfoItem packageInfo) async {
    try {
      final userProvider = Provider.of<UserProvider>(context, listen: false);
      final ticket = userProvider.currentTicket;

      if (ticket == null || ticket.isEmpty) {
        _showErrorDialog('用户登录信息已过期，请重新登录');
        return;
      }

      LogUtil.d('Starting game binding, package name: ${packageInfo.packageName}');

      // Create game binding info
      final gameBindingInfo = GameBindingInfo(
        appTicket: ticket,
        appId: AppConfig.appId,
        appPid: AppConfig.pid,
        appGid: AppConfig.gid,
        traceId: GameBindingInfo.generateTraceId(),
      );

      LogUtil.d("Game binding params: ${jsonEncode(gameBindingInfo.toJson())}");

      // Use ChannelManager to launch game
      await ChannelManager().bindingGame(
        packageName: packageInfo.packageName,
        gameBindingInfo: gameBindingInfo,
        gameExtParams: {
          'dialogContent': '请确认授权当前登录信息给《斗罗宇宙》'
        },
      );

      LogUtil.d('Game launched successfully');

      // Return to previous page after successful launch
      if (mounted) {
        Navigator.of(context).pop();
      }
    } catch (e) {
      LogUtil.e('Exception launching game: $e');
      _showErrorDialog('启动游戏失败: $e');
    }
  }


  /// Show error dialog
  void _showErrorDialog(String message) {
    if (!mounted) return;
    
    showDialog(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          title: const Text('错误'),
          content: Text(message),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(),
              child: const Text('确定'),
            ),
          ],
        );
      },
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.grey[50],
      appBar: AppBar(
        backgroundColor: Colors.white,
        elevation: 0,
        leading: IconButton(
          icon: const Icon(Icons.arrow_back, color: Colors.black),
          onPressed: () => Navigator.pop(context),
        ),
        title: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            // Game icon and name from CommunityPage config
            Consumer<GameCircleConfigProvider>(
              builder: (context, configProvider, child) {
                final gameIcon = configProvider.gameIcon;
                final gameName = configProvider.gameName;
                
                return Row(
                  children: [
                    if (gameIcon != null)
                      ClipRRect(
                        borderRadius: BorderRadius.circular(4),
                        child: Image.network(
                          gameIcon,
                          width: 24,
                          height: 24,
                          fit: BoxFit.cover,
                          errorBuilder: (context, error, stackTrace) {
                            return Container(
                              width: 24,
                              height: 24,
                              color: Colors.grey[300],
                              child: const Icon(
                                Icons.games,
                                size: 16,
                                color: Colors.grey,
                              ),
                            );
                          },
                        ),
                      ),
                    const SizedBox(width: 8),
                    Text(
                      gameName ?? '选择游戏包',
                      style: const TextStyle(
                        color: Colors.black,
                        fontSize: 18,
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                  ],
                );
              },
            ),
          ],
        ),
        centerTitle: true,
      ),
      body: _buildBody(),
    );
  }

  Widget _buildBody() {
    if (_isLoading) {
      return const Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            CircularProgressIndicator(),
            SizedBox(height: 16),
            Text(
              '正在加载游戏包信息...',
              style: TextStyle(
                fontSize: 14,
                color: Colors.grey,
              ),
            ),
          ],
        ),
      );
    }

    if (_error != null) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.error_outline,
              size: 64,
              color: Colors.grey[400],
            ),
            const SizedBox(height: 16),
            Text(
              _error!,
              style: TextStyle(
                fontSize: 16,
                color: Colors.grey[600],
              ),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 16),
            ElevatedButton(
              onPressed: _loadGamePackages,
              child: const Text('重试'),
            ),
          ],
        ),
      );
    }

    if (_packageList.isEmpty) {
      return const Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.games_outlined,
              size: 64,
              color: Colors.grey,
            ),
            SizedBox(height: 16),
            Text(
              '暂无可用的游戏包',
              style: TextStyle(
                fontSize: 16,
                color: Colors.grey,
              ),
            ),
          ],
        ),
      );
    }

    return RefreshIndicator(
      onRefresh: _loadGamePackages,
      child: Column(
        children: [
          // Header section
          Container(
            padding: const EdgeInsets.all(16),
            color: Colors.white,
            child: Column(
              children: [
                const Text(
                  '选择游戏包体进行授权绑定',
                  style: TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.w500,
                    color: Colors.black87,
                  ),
                ),
                const SizedBox(height: 8),
                Text(
                  '点击下方游戏图标完成授权绑定',
                  style: TextStyle(
                    fontSize: 14,
                    color: Colors.grey[600],
                  ),
                ),
              ],
            ),
          ),
          // Games grid
          Expanded(
            child: GridView.builder(
              padding: const EdgeInsets.all(16),
              gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
                crossAxisCount: 3,
                crossAxisSpacing: 16,
                mainAxisSpacing: 16,
                childAspectRatio: 0.8,
              ),
              itemCount: _packageList.length,
              itemBuilder: (context, index) {
                final packageInfo = _packageList[index];
                return _buildGameGridItem(packageInfo);
              },
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildGameGridItem(PackageInfoItem packageInfo) {
    return GestureDetector(
      onTap: () => _launchGame(packageInfo),
      child: Container(
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(12),
          boxShadow: [
            BoxShadow(
              color: Colors.grey.withValues(alpha: 0.1),
              spreadRadius: 1,
              blurRadius: 3,
              offset: const Offset(0, 1),
            ),
          ],
        ),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            // Game icon
            Container(
              width: 60,
              height: 60,
              decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(12),
                boxShadow: [
                  BoxShadow(
                    color: Colors.grey.withValues(alpha: 0.2),
                    spreadRadius: 1,
                    blurRadius: 3,
                    offset: const Offset(0, 1),
                  ),
                ],
              ),
              child: ClipRRect(
                borderRadius: BorderRadius.circular(12),
                child: packageInfo.iconUrl.isNotEmpty
                    ? Image.network(
                        packageInfo.iconUrl,
                        fit: BoxFit.cover,
                        errorBuilder: (context, error, stackTrace) {
                          return Container(
                            color: Colors.grey[200],
                            child: const Icon(
                              Icons.games,
                              size: 30,
                              color: Colors.grey,
                            ),
                          );
                        },
                      )
                    : Container(
                        color: Colors.grey[200],
                        child: const Icon(
                          Icons.games,
                          size: 30,
                          color: Colors.grey,
                        ),
                      ),
              ),
            ),
            const SizedBox(height: 8),
            // Game title
            Padding(
              padding: const EdgeInsets.symmetric(horizontal: 8),
              child: Text(
                packageInfo.title,
                style: const TextStyle(
                  fontSize: 14,
                  fontWeight: FontWeight.w500,
                  color: Colors.black87,
                ),
                textAlign: TextAlign.center,
                maxLines: 2,
                overflow: TextOverflow.ellipsis,
              ),
            ),
            // Package type and install status
            const SizedBox(height: 4),
            if (packageInfo.hasInstall)
              Container(
                padding: const EdgeInsets.symmetric(
                  horizontal: 6,
                  vertical: 2,
                ),
                decoration: BoxDecoration(
                  color: Colors.green.withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(4),
                ),
                child: Text(
                  '已安装',
                  style: TextStyle(
                    fontSize: 10,
                    color: Colors.green[700],
                    fontWeight: FontWeight.w500,
                  ),
                ),
              )
            else
              Text(
                _getPackageTypeDescription(packageInfo.packageType),
                style: TextStyle(
                  fontSize: 10,
                  color: Colors.grey[600],
                ),
                textAlign: TextAlign.center,
                maxLines: 1,
                overflow: TextOverflow.ellipsis,
              ),
          ],
        ),
      ),
    );
  }

  /// Get package type description
  String _getPackageTypeDescription(String packageType) {
    switch (packageType.toLowerCase()) {
      case 'official':
        return '官方包';
      case 'wechat':
        return '微信小游戏';
      case 'other_package':
        return '其他包体';
      default:
        return packageType;
    }
  }
}