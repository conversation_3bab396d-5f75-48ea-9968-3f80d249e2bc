import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../../services/app_route_manager.dart';
import '../../providers/game_circle_provider.dart';
import '../../net/api/game_circle_service.dart';
import '../../model/game_circle.dart';
import '../../utils/log_util.dart';

class GameCirclePage extends StatefulWidget {
  const GameCirclePage({super.key});

  @override
  State<GameCirclePage> createState() => _GameCirclePageState();
}

class _GameCirclePageState extends State<GameCirclePage> {
  List<GameCircle> _gameCircles = [];
  bool _isLoading = true;
  String? _error;

  @override
  void initState() {
    super.initState();
    // 页面初始化时请求圈子列表
    _loadGameCircleList();
  }

  /// 请求游戏圈子列表
  Future<void> _loadGameCircleList() async {
    try {
      LogUtil.d('开始请求游戏圈子列表...');
      GameCircleService.context = context;
      final response = await GameCircleService.getGameCircleList(context: context);
      
      LogUtil.d('游戏圈子列表请求完成');
      LogUtil.d('响应码: ${response.code}');
      LogUtil.d('响应消息: ${response.message}');
      LogUtil.d('响应数据: ${response.data}');
      
      if (response.success && response.data != null) {
        LogUtil.d('游戏圈子列表请求成功');
        setState(() {
          _gameCircles = response.data!.circles;
          _isLoading = false;
          _error = null;
        });
      } else {
        LogUtil.e('游戏圈子列表请求失败: ${response.message}');
        setState(() {
          _isLoading = false;
          _error = response.message ?? '请求失败';
        });
      }
    } catch (e) {
      LogUtil.e('请求游戏圈子列表异常: $e');
      setState(() {
        _isLoading = false;
        _error = '网络异常: $e';
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.white,
      appBar: AppBar(
        backgroundColor: Colors.white,
        elevation: 0,
        title: const Text(
          '请选择游戏圈',
          style: TextStyle(
            color: Colors.black,
            fontSize: 18,
            fontWeight: FontWeight.w600,
          ),
        ),
        centerTitle: true,
        automaticallyImplyLeading: false,
      ),
      body: Padding(
        padding: const EdgeInsets.all(16.0),
        child: _buildContent(),
      ),
    );
  }

  Widget _buildContent() {
    if (_isLoading) {
      return const Center(child: CircularProgressIndicator());
    }

    if (_error != null) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(Icons.error_outline, size: 64, color: Colors.grey[400]),
            const SizedBox(height: 16),
            Text(
              _error!,
              style: TextStyle(fontSize: 16, color: Colors.grey[600]),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 16),
            ElevatedButton(
              onPressed: () {
                setState(() {
                  _isLoading = true;
                  _error = null;
                });
                _loadGameCircleList();
              },
              child: const Text('重试'),
            ),
          ],
        ),
      );
    }

    if (_gameCircles.isEmpty) {
      return const Center(
        child: Text(
          '暂无可选择的游戏圈',
          style: TextStyle(fontSize: 16, color: Colors.grey),
        ),
      );
    }

    return GridView.builder(
      gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
        crossAxisCount: 3,
        crossAxisSpacing: 12,
        mainAxisSpacing: 16,
        childAspectRatio: 0.8,
      ),
      itemCount: _gameCircles.length,
      itemBuilder: (context, index) {
        final circle = _gameCircles[index];
        return GameCircleCard(
          circle: circle,
          onTap: () => _onGameCircleTap(circle),
        );
      },
    );
  }

  void _onGameCircleTap(GameCircle circle) async {
    // 处理游戏圈点击事件
    LogUtil.d('选择了游戏圈: ${circle.displayName}');
    
    // 保存游戏圈选择状态，包括完整的圈子数据
    final gameCircleProvider = Provider.of<GameCircleProvider>(context, listen: false);
    await gameCircleProvider.selectGameCircle(circle.circleId, circle.displayName, circle);
    
    // 选择游戏圈后跳转到主页
    if (mounted) {
      AppRouteManager.navigateToHome(context);
    }
  }
}

class GameCircleCard extends StatelessWidget {
  final GameCircle circle;
  final VoidCallback onTap;

  const GameCircleCard({
    super.key,
    required this.circle,
    required this.onTap,
  });

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: onTap,
      child: Column(
        children: [
          // 圆角游戏图标 - 固定在顶部
          Container(
            width: 80,
            height: 80,
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(12),
              border: Border.all(
                color: Colors.grey.withOpacity(0.2),
                width: 1,
              ),
            ),
            child: ClipRRect(
              borderRadius: BorderRadius.circular(12),
              child: Image.network(
                circle.circleIcon,
                width: 80,
                height: 80,
                fit: BoxFit.cover,
                errorBuilder: (context, error, stackTrace) {
                  return Container(
                    width: 80,
                    height: 80,
                    decoration: BoxDecoration(
                      color: Colors.grey.withOpacity(0.1),
                      borderRadius: BorderRadius.circular(12),
                    ),
                    child: const Icon(
                      Icons.gamepad,
                      color: Colors.grey,
                      size: 40,
                    ),
                  );
                },
              ),
            ),
          ),
          const SizedBox(height: 12),
          // 游戏名称 - 使用Expanded占据剩余空间
          Expanded(
            child: Padding(
              padding: const EdgeInsets.symmetric(horizontal: 8),
              child: Text(
                circle.displayName,
                style: const TextStyle(
                  fontSize: 12,
                  fontWeight: FontWeight.w500,
                  color: Color(0xFF333333),
                ),
                textAlign: TextAlign.center,
                maxLines: 2,
                overflow: TextOverflow.ellipsis,
              ),
            ),
          ),
        ],
      ),
    );
  }
}