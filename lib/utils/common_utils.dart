

class CommonUtils {

  /// 判断是否为视频格式
  static bool isVideoLink(String url) {
    if (url.isEmpty) return false;

    // 常见的视频文件扩展名
    final videoExtensions = [
      '.mp4', '.avi', '.mov', '.wmv', '.flv', '.mkv',
      '.webm', '.m4v', '.3gp', '.3g2', '.mpg', '.mpeg',
      '.ts', '.mts', '.m2ts'
    ];

    // 转换为小写进行比较
    final lowerUrl = url.toLowerCase();

    // 检查URL是否以视频扩展名结尾
    for (final extension in videoExtensions) {
      if (lowerUrl.endsWith(extension)) {
        return true;
      }
    }

    // 检查URL中是否包含视频扩展名
    for (final extension in videoExtensions) {
      if (lowerUrl.contains(extension + '?') || lowerUrl.contains(extension + '#')) {
        return true;
      }
    }

    return false;
  }

  /// 将字节大小转换为更友好的单位（KB、MB或GB）
  static String formatBytes(String? bytesString) {
    if (bytesString == null || bytesString.isEmpty) {
      return "未知大小";
    }

    final bytes = int.tryParse(bytesString);
    if (bytes == null) {
      return "未知大小";
    }

    const divider = 1024;
    if (bytes < divider) {
      return "$bytes B";
    }

    if (bytes < divider * divider) {
      return "${(bytes / divider).toStringAsFixed(2)} KB";
    }

    if (bytes < divider * divider * divider) {
      return "${(bytes / (divider * divider)).toStringAsFixed(2)} MB";
    }

    return "${(bytes / (divider * divider * divider)).toStringAsFixed(2)} GB";
  }

}