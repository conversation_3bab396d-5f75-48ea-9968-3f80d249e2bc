import 'package:flutter/material.dart';
import '../pages/community/community_detail_page.dart';
import '../pages/community/collection_page.dart';
import '../pages/community/topic_page.dart';
import '../model/forum_post_list.dart';
import '../webview/webview_dialog.dart';

enum UrlType {
  topic,
  collection,
  thread,
}

class UrlParseResult {
  final UrlType type;
  final int id;

  const UrlParseResult({
    required this.type,
    required this.id,
  });
}

/// 活动链接处理工具类
class ActivityLinkHandler {
  /// 处理活动点击事件 - 静态方法，外部可调用
  static void handleActivityClick(BuildContext context, String link, String title) {
    // 提取url类型
    UrlParseResult? urlParseResult = _extractIdFromUrl(link);
    if (urlParseResult != null){
      UrlType type = urlParseResult.type;
      int id = urlParseResult.id;
      switch (type) {
        //打开帖子页
        case UrlType.thread:
        // 创建一个简化的ForumPost对象，只设置必要的字段
          final post = _createSimpleForumPost(id, title);
          // 跳转到帖子详情页
          Navigator.push(
            context,
            MaterialPageRoute(
              builder: (context) => CommunityDetailPage(post: post),
            ),
          );
          break;
        //打开集合页
        case UrlType.collection:
          Navigator.push(
            context,
            MaterialPageRoute(
              builder: (context) => CollectionPage(
                collectionId: id,
                title: title,
              ),
            ),
          );
          break;
        //打开话题页
        case UrlType.topic:
          Navigator.push(
            context,
            MaterialPageRoute(
              builder:
                  (context) => TopicPage(
                topicName: '',
                topicId: id,
              ),
            ),
          );
          break;
      }
    }else{
      // 如果无法提取Id，使用webview打开module.url
      debugPrint('无法从URL中提取Id，使用webview打开: ${link}');
      if (!link.contains('http')) {
        debugPrint('URL非网页地址，无法使用webview打开: ${link}');
        return;
      }

      // 使用webview弹窗打开URL
      Navigator.push(
        context,
        MaterialPageRoute(
          builder:
              (context) => Scaffold(
                backgroundColor: Colors.white,
                appBar: AppBar(
                  title: Text(title),
                  backgroundColor: Colors.white,
                  foregroundColor: Colors.black,
                  elevation: 0,
                  leading: IconButton(
                    icon: const Icon(Icons.close),
                    onPressed: () => Navigator.of(context).pop(),
                  ),
                ),
                body: WebViewDialog(
                  url: link,
                  title: title,
                  showToolBar: false, // 不显示底部工具栏，使用AppBar
                ),
              ),
        ),
      );
    }
  }

  /// 从URL中提取ID和类型
  ///
  /// 支持的URL类型：
  /// - 话题: https://forum.odchqpto.com/pages/search/recommend-topic?tgid=5636&topicId=31&name=%E7%A6%8F%E5%88%A9
  /// - 集合页: https://forum.odchqpto.com/pages/collect/index?tgid=1&collectionId=1&title=rjtest
  /// - 帖子: https://forum.odchqpto.com/pages/thread/index?id=219037&tgid=1
  static UrlParseResult? _extractIdFromUrl(String url) {
    try {
      final uri = Uri.parse(url);

      if (url.contains('topicId')) {
        final topicId = uri.queryParameters['topicId'];
        if (topicId != null) {
          final id = int.tryParse(topicId);
          if (id != null && id > 0) {
            return UrlParseResult(type: UrlType.topic, id: id);
          }
        }
      }

      if (url.contains('collectionId')) {
        final collectionId = uri.queryParameters['collectionId'];
        if (collectionId != null) {
          final id = int.tryParse(collectionId);
          if (id != null && id > 0) {
            return UrlParseResult(type: UrlType.collection, id: id);
          }
        }
      }

      if (url.contains('thread') && uri.queryParameters.containsKey('id')) {
        var threadId;
        if (url.contains('threadId')){
          threadId = uri.queryParameters['threadId'];
        }else{
          threadId = uri.queryParameters['id'];
        }
        if (threadId != null) {
          final id = int.tryParse(threadId);
          if (id != null && id > 0) {
            return UrlParseResult(type: UrlType.thread, id: id);
          }
        }
      }

      return null;
    } catch (e) {
      debugPrint('解析URL时出错: $e');
      return null;
    }
  }

  /// 创建简化的ForumPost对象
  static ForumPost _createSimpleForumPost(int threadId, String title) {
    return ForumPost(
      threadId: threadId,
      postId: 0,
      userId: 0,
      categoryId: 0,
      parentCategoryId: 0,
      topicId: 0,
      categoryName: '',
      parentCategoryName: '',
      title: title,
      displayTitle: title,
      viewCount: 0,
      isApproved: 1,
      isStick: false,
      isDraft: false,
      isSite: false,
      isAnonymous: false,
      isFavorite: false,
      price: 0.0,
      attachmentPrice: 0.0,
      payType: 0,
      paid: null,
      isLike: false,
      isReward: false,
      createdAt: '',
      issueAt: '',
      updatedAt: '',
      diffTime: '',
      user: ForumUser(
        userId: 0,
        nickname: '',
        avatar: '',
        badge: '',
        label: '',
        color: '',
        medal: null,
        threadCount: 0,
        followCount: 0,
        fansCount: 0,
        likedCount: 0,
        questionCount: 0,
        isRealName: false,
        joinedAt: '',
        follow: 0,
      ),
      group: null,
      likeReward: ForumLikeReward(
        users: [],
        likePayCount: 0,
        shareCount: 0,
        postCount: 0,
      ),
      displayTag: ForumDisplayTag(
        isPoster: false,
        isEssence: false,
        isRedPack: null,
        isReward: null,
        isVote: false,
      ),
      position: ForumPosition(
        longitude: '',
        latitude: '',
        address: '',
        location: '',
      ),
      ability: ForumAbility(
        canEdit: false,
        canDelete: false,
        canEssence: false,
        canPoster: false,
        canStick: false,
        canReply: false,
        canViewPost: false,
        canFreeViewPost: false,
        canViewVideo: false,
        canViewAttachment: false,
        canDownloadAttachment: false,
      ),
      content: ForumContent(
        text: '',
        indexes: {},
        poster: null,
        images: [],
        videos: [],
        covers: [],
        pureText: '',
      ),
      freewords: 0,
      userStickStatus: false,
      favorCount: 0,
      topics: '',
      reportStatus: 0,
      startShowTime: '',
      isBottom: 0,
      aiRank: '',
      aiType: '',
      pid: 0,
      uid: 0,
      gamePay: 0,
      auditedBy: '',
      auditedAt: null,
      updatedBy: '',
      createdBy: '',
      location: '',
      from: 0,
      addAiContent: 0,
      isRecommend: 0,
      voteId: null,
      vote: null,
      isMixThread: 0,
    );
  }
}
