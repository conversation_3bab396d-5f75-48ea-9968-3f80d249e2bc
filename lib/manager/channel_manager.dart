import 'dart:convert';
import 'dart:io';

import 'package:dlyz_flutter/model/connectivity_result.dart';
import 'package:dlyz_flutter/model/game_binding_info.dart';
import 'package:dlyz_flutter/utils/device_info_util.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/services.dart';
import 'package:fluttertoast/fluttertoast.dart';
import 'package:package_info_plus/package_info_plus.dart';
import 'package:provider/provider.dart';
import 'package:flutter/material.dart';

import '../pay/pay_type.dart';
import '../model/mini_program_info.dart';
import '../providers/login_provider.dart';

/// 与原生通信的管理类
class ChannelManager {
  /// 单例实例
  static final ChannelManager _instance = ChannelManager._internal();

  /// 通信通道名称，必须与原生端保持一致
  static const String _channelName = 'channel.control/dlyz';
  static const String _eventName = 'channel.control/dlyz_status';

  /// 方法通道实例
  final MethodChannel _methodChannel = const MethodChannel(_channelName);
  final EventChannel _eventChannel = const EventChannel(_eventName);

  /// 闪验UI事件回调
  Function(String method, Map<String, dynamic> arguments)? _fastLoginUIEventCallback;

  /// 游戏绑定成功回调
  Function(String bindingRespData)? _bindingSuccessCallback;

  /// 网络状态监听
  Stream<List<ConnectivityResult>>? _onConnectivityChanged;

  /// 当前应用的 BuildContext，用于获取 Provider
  BuildContext? _context;

  /// 工厂构造函数，返回单例
  factory ChannelManager() {
    return _instance;
  }

  /// 私有构造函数
  ChannelManager._internal() {
    // 设置方法调用处理器，监听来自原生端的事件
    _methodChannel.setMethodCallHandler(_handleMethodCall);
  }

  /// 设置应用上下文
  void setCurContext(BuildContext context) {
    _context = context;
  }

  void releaseContext() {
    _context = null;
  }

  /// 监听网络状态变化
  Stream<List<ConnectivityResult>> get onConnectivityChanged {
    _onConnectivityChanged ??= _eventChannel
        .receiveBroadcastStream()
        .map((dynamic result) => List<String>.from(result))
        .map(DeviceInfoUtil.parseConnectivityResults);
    return _onConnectivityChanged!;
  }

  Future<List<ConnectivityResult>> checkNetworkConnectivity() {
    return _methodChannel
        .invokeListMethod<String>('checkNetworkType')
        .then((value) => DeviceInfoUtil.parseConnectivityResults(value ?? []));
  }

  /// 获取平台版本
  Future<String?> getPlatformVersion() async {
    try {
      final String? version = await _methodChannel.invokeMethod('getPlatformVersion');
      return version;
    } on PlatformException catch (e) {
      print('获取平台版本失败: ${e.message}');
      return null;
    }
  }

  /// 检查游戏是否已安装
  /// [packageName] 游戏包名
  Future<bool> checkGameInstalled({required String packageName}) async {
    try {
      final bool isInstalled = await _methodChannel.invokeMethod(
        'checkGameInstalled',
        {'packageName': packageName},
      );
      return isInstalled;
    } on PlatformException catch (e) {
      print('检查游戏安装状态失败: ${e.code} - ${e.message}');
      return false;
    }
  }

  /// 打开已安装的游戏
  /// [packageName] 游戏包名
  Future<bool?> openInstalledGame({required String packageName}) async {
    try {
      final bool? result = await _methodChannel.invokeMethod(
        'openInstalledGame',
        {'packageName': packageName},
      );
      return result;
    } on PlatformException catch (e) {
      print('打开游戏失败: ${e.code} - ${e.message}');
      return null;
    }
  }

  /// 安装APK文件
  /// [filePath] APK文件路径
  Future<void> installApk(String filePath) async {
    try {
      await _methodChannel.invokeMethod('installApk', {'filePath': filePath});
    } on PlatformException catch (e) {
      print('Failed to install APK: ${e.message}');
      throw Exception('安装失败: ${e.message}');
    }
  }

  /// 打开指定URL
  Future<bool?> openUrl({required String url}) async {
    try {
      final bool? result = await _methodChannel.invokeMethod(
        'openUrl',
        {'url': url},
      );
      return result;
    } on PlatformException catch (e) {
      print('打开URL失败: ${e.code} - ${e.message}');
      return false;
    }
  }

  /// 打开小程序
  Future<void> jumpToMiniProgram({required MiniProgramInfo info}) async {
    try {
      await _methodChannel.invokeMethod(
          'jumpToMiniProgram',
          {
            'skip_type': info.skipType,
            'app_id': info.appId,
            'mini_program_id': info.miniProgramId,
            'mini_program_path': info.miniProgramPath,
            'scheme_url': info.schemeUrl
          }
      );
    } on PlatformException catch (e) {
      print('Failed to install APK: ${e.message}');
      throw Exception('跳转失败: ${e.message}');
    }
  }

  /// 获取平台版本
  Future<String?> getAndroidId() async {
    try {
      final String? android_id = await _methodChannel.invokeMethod('getAndroidId');
      return android_id;
    } on PlatformException catch (e) {
      print('获取android失败: ${e.message}');
      return null;
    }
  }

  /// 打开已安装的游戏，走绑定流程
  /// [packageName] 游戏包名
  /// [gameBindingInfo] api接口参数
  /// [gameExtParams] sdk需要的扩展参数
  Future<bool?> bindingGame({
    required String packageName,
    required GameBindingInfo gameBindingInfo,
    required Map<String, dynamic> gameExtParams
  }) async {
    try {
      PackageInfo info = await PackageInfo.fromPlatform();
      Map<String, dynamic> apiParams = gameBindingInfo.toJson();
      Map<String, dynamic> extParams = {...gameExtParams, 'callingPackageName': info.packageName};
      Map<String, dynamic> bindingParams = {
        'apiParams': apiParams,
        'extParams': extParams
      };
      String paramsJson = jsonEncode(bindingParams);
      final bool? result = await _methodChannel.invokeMethod(
        'bindingGame',
        {
          'packageName': packageName,
          'bindingParams': paramsJson
        },
      );
      return result;
    } on PlatformException catch (e) {
      print('打开游戏失败: ${e.code} - ${e.message}');
      return null;
    }
  }

  Future<PayResult> androidPay({
    required PayType payType,
    required String orderId,
    required String tradeInfo
  }) async {
    try {
      Map<String, dynamic> params = {
        'payType': payType.name,
        'orderId': orderId,
        'tradeInfo': tradeInfo
      };
      final bool? result = await _methodChannel.invokeMethod('pay', params);
      if (result == null || !result) {
        return PayResult(code: PayResult.PAY_FAILED, message: '支付失败');
      }
      return PayResult(code: PayResult.PAY_SUCCESS, message: '支付成功');
    } on PlatformException catch (e) {
      print('支付失败: ${e.code} - ${e.message}');
      return PayResult(code: e.code, message: e.message ?? "未知错误");
    }
  }

  /// 设置闪验配置
  Future<bool> setFastLoginConfig(Map<String, dynamic> config, {
    String? userAgreementUrl,
    String? privacyPolicyUrl,
  }) async {
    try {
      // 将协议地址添加到配置中
      final Map<String, dynamic> fullConfig = Map.from(config);
      if (userAgreementUrl != null) {
        fullConfig['userAgreementUrl'] = userAgreementUrl;
      }
      if (privacyPolicyUrl != null) {
        fullConfig['privacyPolicyUrl'] = privacyPolicyUrl;
      }

      final bool? success = await _methodChannel.invokeMethod(
        'setFastLoginConfig',
        fullConfig,
      );
      return success ?? false;
    } on PlatformException catch (e) {
      print('设置闪验配置失败: ${e.code} - ${e.message}');
      return false;
    }
  }

  /// 检查闪验环境是否支持
  Future<bool> checkFastLoginEnvironment() async {
    try {
      final bool? supported = await _methodChannel.invokeMethod('checkFastLoginEnvironment');
      return supported ?? false;
    } on PlatformException catch (e) {
      print('检查闪验环境失败: ${e.code} - ${e.message}');
      return false;
    }
  }

  /// 初始化闪验SDK
  Future<bool> initializeFastLogin() async {
    try {
      final bool? success = await _methodChannel.invokeMethod('initializeFastLogin');
      return success ?? false;
    } on PlatformException catch (e) {
      print('初始化闪验失败: ${e.code} - ${e.message}');
      throw FastLoginException(e.code, e.message ?? 'Unknown error');
    }
  }

  /// 执行闪验登录
  Future<Map<String, dynamic>> doFastLogin() async {
    try {
      final result = await _methodChannel.invokeMethod('doFastLogin');
      return Map<String, dynamic>.from(result);
    } on PlatformException catch (e) {
      if (e.code == 'CANCEL') {
        throw FastLoginCancelException();
      }
      throw FastLoginException(e.code, e.message ?? 'Unknown error');
    }
  }

  /// 设置闪验UI事件监听器
  void setFastLoginUIEventListener(Function(String method, Map<String, dynamic> arguments)? callback) {
    _fastLoginUIEventCallback = callback;
  }

  /// 设置游戏绑定成功回调
  void setBindingSuccessCallback(Function(String bindingRespData)? callback) {
    _bindingSuccessCallback = callback;
  }

  /// 获取IDFA (仅iOS)
  Future<String?> getIDFA() async {
    if (!Platform.isIOS) {
      return null;
    }
    
    try {
      final String? idfa = await _methodChannel.invokeMethod('getIDFA');
      return idfa;
    } on PlatformException catch (e) {
      print('获取IDFA失败: ${e.code} - ${e.message}');
      return null;
    }
  }

  /// 请求广告追踪权限 (仅iOS)
  Future<String> requestTrackingPermission() async {
    if (!Platform.isIOS) {
      return "not_supported";
    }
    
    try {
      final String status = await _methodChannel.invokeMethod('requestTrackingPermission');
      return status;
    } on PlatformException catch (e) {
      print('请求广告追踪权限失败: ${e.code} - ${e.message}');
      return "error";
    }
  }

  /// 处理来自原生端的方法调用
  Future<dynamic> _handleMethodCall(MethodCall call) async {
    switch (call.method) {
      case 'onFastLoginUIEvent':
        _handleFastLoginUIEvent(call.arguments);
        break;
      case 'onBindingSuccess':
        _handlerBindingSuccess(call.arguments);
        break;
      case 'checkAuthAppStatusIfNeeded':
        return _handleCheckAuthAppStatusIfNeeded();
      default:
        print('未处理的方法调用: ${call.method}');
    }
  }

  /// 处理检查授权应用状态
  Future<bool> _handleCheckAuthAppStatusIfNeeded() async {
    try {
      if (_context != null && _context!.mounted) {
        final loginProvider = Provider.of<LoginStateProvider>(_context!, listen: false);
        // 检查当前是否正在进行微信登录
        if (!loginProvider.weChatLoginState) {
          debugPrint('当前没有微信登录状态监听，跳过检查');
          return false;
        }
        
        // 保存当前微信登录状态
        bool wasWeChatLoginActive = loginProvider.weChatLoginState;
        await loginProvider.checkAuthAppStatusIfNeeded(_context);
        // 检查微信登录状态是否从true变为false，并且结果是成功
        bool stateChanged = wasWeChatLoginActive && !loginProvider.weChatLoginState;
        bool isLoginSuccess = stateChanged && loginProvider.weChatLoginResult == WeChatLoginResult.success;
        return isLoginSuccess;
      }
      return false;
    } catch (e) {
      debugPrint('处理checkAuthAppStatusIfNeeded异常: $e');
      return false;
    }
  }

  /// 处理闪验UI事件
  void _handleFastLoginUIEvent(dynamic arguments) {
    if (arguments is Map) {
      final String? event = arguments['event'];
      switch (event) {
        case 'close_clicked':
          print('Flutter: 用户点击了关闭按钮 - 弹窗已关闭');
          _fastLoginUIEventCallback?.call('close_clicked', {});
          break;
        case 'back_clicked':
          print('Flutter: 用户点击了返回按钮 - 弹窗已关闭');
          _fastLoginUIEventCallback?.call('back_clicked', {});
          break;
        case 'wechat_game_selected':
          print('Flutter: 用户选择了微信小游戏');
          // 提取游戏选择的详细信息
          final gameArguments = {
            'game_title': arguments['game_title'] ?? '',
            'mini_program_id': arguments['mini_program_id'] ?? '',
            'tgid': arguments['tgid'] ?? '',
          };
          _fastLoginUIEventCallback?.call('wechat_game_selected', gameArguments);
          break;
        case 'account_login_clicked':
          print('Flutter: 用户选择了账号密码登录');
          _fastLoginUIEventCallback?.call('account_login_clicked', {});
          break;
        case 'other_phone_clicked':
          print('Flutter: 用户选择了其他手机号登录');
          _fastLoginUIEventCallback?.call('other_phone_clicked', {});
          break;
        case 'forget_password_clicked':
          print('Flutter: 用户点击了忘记密码');
          _fastLoginUIEventCallback?.call('forget_password_clicked', {});
          break;
        case 'show_protocol_click_tip':
          _fastLoginUIEventCallback?.call('show_protocol_click_tip', {});
          break;
        default:
          print('Flutter: 未知的闪验UI事件: $event');
          if (event is String) {
            _fastLoginUIEventCallback?.call(event, {});
          }
      }
    }
  }

  ///处理sdk绑定成功事件
  void _handlerBindingSuccess(dynamic arguments) {
    if (arguments is Map) {
      final String bindingRespData = arguments['bindingRespData'] ?? "";
      if (kDebugMode) {
        Fluttertoast.showToast(
          msg: bindingRespData,
          toastLength: Toast.LENGTH_LONG,
          fontSize: 18.0,
        );
      }
      _bindingSuccessCallback?.call(bindingRespData);
    }
  }
}

/// 闪验异常类
class FastLoginException implements Exception {
  final String code;
  final String message;

  FastLoginException(this.code, this.message);

  @override
  String toString() => 'FastLoginException($code): $message';
}

/// 闪验取消异常
class FastLoginCancelException extends FastLoginException {
  FastLoginCancelException() : super('CANCEL', '用户取消登录');
}