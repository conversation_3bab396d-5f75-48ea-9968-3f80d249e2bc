import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:dlyz_flutter/common/dl_color.dart';
import 'simple_game_package_selector.dart';
import '../model/package_info.dart';
import '../model/game_binding_info.dart';
import '../providers/user_provider.dart';
import '../config/app_config.dart';
import '../manager/channel_manager.dart';
import '../utils/log_util.dart';
import 'dart:convert';

/// 游戏授权绑定底部弹窗
class GameAuthorizationDialog extends StatefulWidget {
  final VoidCallback? onClose;

  const GameAuthorizationDialog({
    super.key,
    this.onClose,
  });

  /// 显示游戏授权绑定弹窗
  static void show({
    required BuildContext context,
    VoidCallback? onClose,
  }) {
    showModalBottomSheet<String>(
      context: context,
      backgroundColor: Colors.transparent,
      isScrollControlled: true,
      builder: (context) => GameAuthorizationDialog(
        onClose: onClose,
      ),
    ).then((result) {
      // 当dialog被关闭时，如果不是通过特定按钮关闭的，则触发onClose回调
      if (result == null) {
        onClose?.call();
      }
    });
  }

  @override
  State<GameAuthorizationDialog> createState() => _GameAuthorizationDialogState();
}

class _GameAuthorizationDialogState extends State<GameAuthorizationDialog> {
  PackageInfoItem? _selectedPackage;

  @override
  Widget build(BuildContext context) {
    return Container(
      decoration: const BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.only(
          topLeft: Radius.circular(20),
          topRight: Radius.circular(20),
        ),
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          _buildHeader(),
          _buildGameContent(),
          _buildBottomButtons(),
        ],
      ),
    );
  }

  /// 构建头部
  Widget _buildHeader() {
    return Container(
      padding: const EdgeInsets.all(20),
      child: const Text(
        '选择游戏包体',
        style: TextStyle(
          fontSize: 18,
          fontWeight: FontWeight.w600,
          color: DLColor.textPrimary,
        ),
        textAlign: TextAlign.center,
      ),
    );
  }

  /// 构建游戏内容区域
  Widget _buildGameContent() {
    return SimpleGamePackageSelector(
      onPackageSelected: (package) {
        setState(() {
          _selectedPackage = package;
        });
      },
    );
  }

  /// 构建底部按钮
  Widget _buildBottomButtons() {
    return Container(
      padding: EdgeInsets.only(
        left: 20,
        right: 20,
        top: 16,
        bottom: MediaQuery.of(context).padding.bottom + 16,
      ),
      child: Row(
        children: [
          // 取消按钮
          Expanded(
            child: GestureDetector(
              onTap: () {
                Navigator.pop(context, 'cancel');
              },
              child: Container(
                height: 50,
                decoration: BoxDecoration(
                  color: Colors.grey[100],
                  borderRadius: BorderRadius.circular(12),
                ),
                child: const Center(
                  child: Text(
                    '取消',
                    style: TextStyle(
                      fontSize: 16,
                      color: DLColor.textSecondary,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                ),
              ),
            ),
          ),
          const SizedBox(width: 12),
          // 打开按钮
          Expanded(
            child: GestureDetector(
              onTap: _selectedPackage != null ? () => _launchGame(_selectedPackage!) : null,
              child: Container(
                height: 50,
                decoration: BoxDecoration(
                  color: _selectedPackage != null ? Colors.blue[600] : Colors.grey[300],
                  borderRadius: BorderRadius.circular(12),
                ),
                child: const Center(
                  child: Text(
                    '打开',
                    style: TextStyle(
                      fontSize: 16,
                      color: Colors.white,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  /// 启动游戏
  Future<void> _launchGame(PackageInfoItem packageInfo) async {
    try {
      final userProvider = Provider.of<UserProvider>(context, listen: false);
      final ticket = userProvider.currentTicket;

      if (ticket == null || ticket.isEmpty) {
        _showErrorDialog('用户登录信息已过期，请重新登录');
        return;
      }

      LogUtil.d('Starting game binding, package name: ${packageInfo.packageName}');

      // Create game binding info
      final gameBindingInfo = GameBindingInfo(
        appTicket: ticket,
        appId: AppConfig.appId,
        appPid: AppConfig.pid,
        appGid: AppConfig.gid,
        traceId: GameBindingInfo.generateTraceId(),
      );

      LogUtil.d("Game binding params: ${jsonEncode(gameBindingInfo.toJson())}");

      // Use ChannelManager to launch game
      await ChannelManager().bindingGame(
        packageName: packageInfo.packageName,
        gameBindingInfo: gameBindingInfo,
        gameExtParams: {
          'dialogContent': '请确认授权当前登录信息给《斗罗宇宙》'
        },
      );

      LogUtil.d('Game launched successfully');

      // 不关闭弹窗，让用户可以继续操作
    } catch (e) {
      LogUtil.e('Exception launching game: $e');
      _showErrorDialog('启动游戏失败: $e');
    }
  }

  /// 显示错误对话框
  void _showErrorDialog(String message) {
    if (!mounted) return;
    
    showDialog(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          title: const Text('错误'),
          content: Text(message),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(),
              child: const Text('确定'),
            ),
          ],
        );
      },
    );
  }
}