import 'package:flutter/material.dart';
import 'package:dlyz_flutter/common/dl_color.dart';

import 'cache_image.dart';
import '../pages/bind/switch_bind_character_page.dart';

/// 游戏角色绑定底部弹窗
class GameCharacterBindingDialog extends StatefulWidget {
  final List<GameCharacter> characters;
  final ValueChanged<GameCharacter>? onCharacterSelected;
  final VoidCallback? onBindOtherCharacter;
  final VoidCallback? onClose;

  const GameCharacterBindingDialog({
    super.key,
    required this.characters,
    this.onCharacterSelected,
    this.onBindOtherCharacter,
    this.onClose,
  });

  /// 显示游戏角色绑定弹窗
  static void show({
    required BuildContext context,
    required List<GameCharacter> characters,
    ValueChanged<GameCharacter>? onCharacterSelected,
    VoidCallback? onBindOtherCharacter,
    VoidCallback? onClose,
  }) {
    showModalBottomSheet<String>(
      context: context,
      backgroundColor: Colors.transparent,
      isScrollControlled: true,
      builder: (context) => GameCharacterBindingDialog(
        characters: characters,
        onCharacterSelected: onCharacterSelected,
        onBindOtherCharacter: onBindOtherCharacter,
        onClose: onClose,
      ),
    ).then((result) {
      // 当dialog被关闭时，如果不是通过特定按钮关闭的，则触发onClose回调
      // result为null表示是通过点击外部区域或返回键关闭的
      if (result == null) {
        onClose?.call();
      }
    });
  }

  @override
  State<GameCharacterBindingDialog> createState() => _GameCharacterBindingDialogState();
}

class _GameCharacterBindingDialogState extends State<GameCharacterBindingDialog> {
  int? selectedIndex;
  List<GameCharacter> characters = [];
  bool isLoading = false;

  @override
  void initState() {
    super.initState();
    _initializeCharacters();
  }

  /// 初始化角色数据
  void _initializeCharacters() {
    setState(() {
      characters = widget.characters;
      // 找到已选择的角色的索引
      selectedIndex = characters.indexWhere((char) => char.isSelected);
      if (selectedIndex == -1) {
        selectedIndex = null;
      }
    });
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      decoration: const BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.only(
          topLeft: Radius.circular(20),
          topRight: Radius.circular(20),
        ),
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          _buildHeader(),
          if (isLoading)
            _buildLoadingState()
          else if (characters.isEmpty)
            _buildEmptyState()
          else
            _buildCharacterList(),
          _buildBindOtherButton(),
          SizedBox(height: MediaQuery.of(context).padding.bottom + 20),
        ],
      ),
    );
  }

  /// 构建头部
  Widget _buildHeader() {
    return Container(
      padding: const EdgeInsets.symmetric(vertical: 20),
      child: Column(
        children: [
          // 顶部指示条
          Container(
            width: 40,
            height: 4,
            decoration: BoxDecoration(
              color: DLColor.divider,
              borderRadius: BorderRadius.circular(2),
            ),
          ),
          const SizedBox(height: 16),
          // 标题
          const Text(
            '游戏角色绑定列表',
            style: TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.w600,
              color: DLColor.textPrimary,
            ),
          ),
        ],
      ),
    );
  }

  /// 构建加载状态
  Widget _buildLoadingState() {
    return Container(
      padding: const EdgeInsets.symmetric(vertical: 40),
      child: const Center(
        child: CircularProgressIndicator(
          valueColor: AlwaysStoppedAnimation<Color>(DLColor.primary),
        ),
      ),
    );
  }

  /// 构建空状态
  Widget _buildEmptyState() {
    return Container(
      padding: const EdgeInsets.symmetric(vertical: 40),
      child: const Center(
        child: Text(
          '暂无绑定角色',
          style: TextStyle(
            fontSize: 16,
            color: DLColor.textThird,
          ),
        ),
      ),
    );
  }

  /// 构建角色列表
  Widget _buildCharacterList() {
    return Container(
      constraints: BoxConstraints(
        maxHeight: MediaQuery.of(context).size.height * 0.5,
      ),
      child: ListView.builder(
        shrinkWrap: true,
        padding: const EdgeInsets.symmetric(horizontal: 20),
        itemCount: characters.length,
        itemBuilder: (context, index) {
          return _buildCharacterItem(characters[index], index);
        },
      ),
    );
  }

  /// 构建单个角色项
  Widget _buildCharacterItem(GameCharacter character, int index) {
    final isSelected = selectedIndex == index;
    
    return GestureDetector(
      onTap: () {
        setState(() {
          selectedIndex = index;
        });
        widget.onCharacterSelected?.call(character);
      },
      child: Container(
        margin: const EdgeInsets.only(bottom: 12),
        padding: const EdgeInsets.all(16),
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(12),
          border: Border.all(
            color: isSelected ? DLColor.primary : DLColor.border,
            width: isSelected ? 2 : 1,
          ),
          boxShadow: [
            BoxShadow(
              color: Colors.grey.withValues(alpha: 0.05),
              spreadRadius: 1,
              blurRadius: 4,
              offset: const Offset(0, 2),
            ),
          ],
        ),
        child: Row(
          children: [
            // 角色头像
            ClipRRect(
              borderRadius: BorderRadius.circular(8),
              child: CachedImage(
                imageUrl: character.avatar,
                width: 48,
                height: 48,
                fit: BoxFit.cover,
              ),
            ),
            const SizedBox(width: 12),
            // 角色信息
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    character.name,
                    style: const TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.w500,
                      color: DLColor.textPrimary,
                    ),
                  ),
                  const SizedBox(height: 4),
                  Text(
                    '${character.server}  ${character.level}',
                    style: const TextStyle(
                      fontSize: 14,
                      color: DLColor.textThird,
                    ),
                  ),
                ],
              ),
            ),
            // 选择状态
            Container(
              width: 24,
              height: 24,
              decoration: BoxDecoration(
                shape: BoxShape.circle,
                border: Border.all(
                  color: isSelected ? DLColor.primary : DLColor.border,
                  width: 2,
                ),
                color: isSelected ? DLColor.primary : Colors.transparent,
              ),
              child: isSelected
                  ? const Icon(
                      Icons.check,
                      color: Colors.white,
                      size: 16,
                    )
                  : null,
            ),
          ],
        ),
      ),
    );
  }

  /// 构建绑定其他角色按钮
  Widget _buildBindOtherButton() {
    return Container(
      margin: const EdgeInsets.all(20),
      child: GestureDetector(
        onTap: () async {
          // 先关闭当前弹窗
          Navigator.pop(context, 'bind_other');
          
          // 跳转到SwitchBindCharacterPage页面
          await Navigator.push(
            context,
            MaterialPageRoute(
              builder: (context) => const SwitchBindCharacterPage(),
            ),
          );
        },
        child: Container(
          width: double.infinity,
          padding: const EdgeInsets.symmetric(vertical: 16),
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.circular(12),
            border: Border.all(
              color: DLColor.border,
              width: 1,
            ),
          ),
          child: const Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Icon(
                Icons.add,
                color: DLColor.textSecondary,
                size: 20,
              ),
              SizedBox(width: 8),
              Text(
                '绑定其他角色',
                style: TextStyle(
                  fontSize: 16,
                  color: DLColor.textSecondary,
                  fontWeight: FontWeight.w500,
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}

/// 游戏角色数据模型
class GameCharacter {
  final String id;
  final String name;
  final String server;
  final String level;
  final String avatar;
  final bool isSelected;

  const GameCharacter({
    required this.id,
    required this.name,
    required this.server,
    required this.level,
    required this.avatar,
    this.isSelected = false,
  });

  GameCharacter copyWith({
    String? id,
    String? name,
    String? server,
    String? level,
    String? avatar,
    bool? isSelected,
  }) {
    return GameCharacter(
      id: id ?? this.id,
      name: name ?? this.name,
      server: server ?? this.server,
      level: level ?? this.level,
      avatar: avatar ?? this.avatar,
      isSelected: isSelected ?? this.isSelected,
    );
  }
}
