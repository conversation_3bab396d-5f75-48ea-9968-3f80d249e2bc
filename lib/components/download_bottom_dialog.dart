import 'dart:io';
import 'package:dlyz_flutter/components/cache_image.dart';
import 'package:dlyz_flutter/components/common_alert.dart';
import 'package:dlyz_flutter/components/select_game_bottom_dialog.dart';
import 'package:dlyz_flutter/manager/channel_manager.dart';
import 'package:dlyz_flutter/manager/game_data_manager.dart';
import 'package:dlyz_flutter/model/game_card_info.dart';
import 'package:dlyz_flutter/model/mini_program_info.dart';
import 'package:dlyz_flutter/net/api/game_list_service.dart';
import 'package:dlyz_flutter/providers/download_provider.dart';
import 'package:dlyz_flutter/services/download/internal/ALDownloaderStringExtension.dart';
import 'package:flutter/material.dart';
import 'package:fluttertoast/fluttertoast.dart';
import '../common/dl_color.dart';
import '../model/download_info.dart';
import '../model/game_post_list.dart';
import 'irregular_button.dart';

class DownloadBottomDialog extends StatelessWidget {
  final BuildContext context;
  final GameListDetail game;

  const DownloadBottomDialog({
    super.key,
    required this.context,
    required this.game
  });

  Future<void> _selectGamePackage() async {

    final gameList = game.gameActionConfig.choiceOptions;
    if (game.gameActionConfig.actionType.isShowChoice()) {
      if (context.mounted) {
        SelectGamePackageBottomDialog.show(
            context: context,
            packages: gameList,
            onConfirm: (game) {
              if (game.packageType.isWeChat()) {
                GameDataManager().requestJumpMiniProgram(game.wechatMiniProgramId);
              } else {
                ChannelManager().openInstalledGame(packageName: game.packageName);
              }
            }
        );
      }
    } else {
      ChannelManager().openInstalledGame(packageName: game.gameActionConfig.directPackage);
    }
  }

  /// 查询是否有小程序
  bool _findMiniProGram() {
    try {
      final _ = game.gameActionConfig.choiceOptions.firstWhere((item) => item.packageType.isWeChat()).wechatMiniProgramId;
      return true;
    } catch (e) {
      return false;
    }
  }

  // 显示底部弹窗的静态方法，接收动态参数
  static void show({
    required BuildContext context,
    required GameListDetail game
  }) {
    showModalBottomSheet(
      context: context,
      backgroundColor: Colors.transparent,
      builder: (context) => Stack(
        alignment: Alignment.topCenter,
        children: [
          DownloadBottomDialog(
            context: context,
            game: game
          ),
          // 顶部突出的游戏图标
          Positioned(
            top: 0, // 向上突出一半
            child: ClipRRect(
              borderRadius: BorderRadius.circular(12),
              child: CachedImage(
                  width: 80,
                  height: 80,
                  imageUrl: game.icon
              ),
            ),
          ),
          // 关闭按钮(左侧)
          Positioned(
            left: 0,
            top: 0,
            child: IconButton(
              icon: const Icon(Icons.keyboard_arrow_down, size: 25),
              color: Colors.white,
              onPressed: () => Navigator.pop(context),
            ),
          ),
        ],
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    bool hashMiniProGram = _findMiniProGram();
    return Container(
      height: 290,
      margin: const EdgeInsets.only(top: 40), // 顶部留出空间放置突出图标
      decoration: const BoxDecoration(
        image: DecorationImage(
            image: AssetImage('assets/images/bottom_dialog_bg.png'),
            fit: BoxFit.fitWidth
        ),
        borderRadius: BorderRadius.vertical(top: Radius.circular(16)),
      ),
      child: Column(
        children: [
          Container(
            padding: const EdgeInsets.fromLTRB(16, 50, 16, 16),
            child: Stack(
              children: [
                Center(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.center,
                    children: [
                      Text(
                        game.tGidName,
                        style: const TextStyle(
                          fontSize: 16,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ),
          // 按钮区域
          Expanded(
            child: Padding(
              padding: const EdgeInsets.all(16),
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  _downloadBtn(game),
                  const SizedBox(height: 12),
                  // 打开按钮
                  if (GameDataManager().countInstalledGames(game.gameActionConfig.choiceOptions) > 1 && !Platform.isIOS) ...[
                    IrregularButton(
                      text: '打开其他游戏包',
                      onPressed: () async {
                        _selectGamePackage();
                      },
                    ),
                    const SizedBox(height: 12),
                  ],
                  // 小游戏按钮
                  if (hashMiniProGram && !Platform.isAndroid) ...[
                    IrregularButton(
                      text: '打开微信小游戏',
                      onPressed: () async {
                        final miniProgram = game.gameActionConfig.choiceOptions.firstWhere((item) => item.packageType.isWeChat()).wechatMiniProgramId;
                        GameDataManager().requestJumpMiniProgram(miniProgram);
                      },
                    )
                  ],
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _downloadBtn(GameListDetail game) {
    return ValueListenableBuilder<String>(
        valueListenable: game.downloadInfo.downloadStatusNotifier,
        builder: (context, value, child) {
          if (value.isEqualTo("打开游戏")) {
            return IrregularButton(
                text: "打开官方包",
                backgroundColor: DLColor.primary,
                onPressed: () => ChannelManager().openInstalledGame(packageName: game.gameActionConfig.directPackage)
            );
          } else if (value.isEqualTo("下载中")) {
            return ValueListenableBuilder<String>(
                valueListenable: game.downloadInfo.progressNotifier,
                builder: (context, progressValue, child) {
                  final progress = double.tryParse(progressValue.replaceAll('%', '')) ?? 0;

                  if (progress > 0) {
                    game.downloadInfo.cacheProgress = progress;
                  }

                  final displayProgress = progress > 0 ? progress : game.downloadInfo.cacheProgress ?? 0;
                  return Stack(
                    alignment: Alignment.center,
                    clipBehavior: Clip.none,
                    children: [
                      _buildDownloadBtn(
                          onDownload: () => DownloadProvider().handleDownload(game.downloadInfo),
                          child: Container(
                            decoration: ShapeDecoration(
                              shape: RoundedRectangleBorder(
                                borderRadius: BorderRadius.circular(6),
                              ),
                            ),
                            clipBehavior: Clip.antiAlias,
                            child: Stack(
                              children: [
                                LinearProgressIndicator(
                                  value: displayProgress / 100,
                                  backgroundColor: DLColor.downloadBackground,
                                  valueColor: const AlwaysStoppedAnimation<Color>(DLColor.primary),
                                  minHeight: 44.0,
                                ),
                                Center(
                                  child: ValueListenableBuilder<String>(
                                      valueListenable: game.downloadInfo.downloadSpeedNotifier,
                                      builder: (context, value, child) {
                                        return Text(
                                            value,
                                            style: TextStyle(
                                              color: Colors.white
                                            )
                                        );
                                      }
                                  ),
                                )
                              ],
                            ),
                          )
                      ),
                      Positioned(
                        top: -10,
                        right: -10,
                        child: Container(
                          padding: EdgeInsets.symmetric(horizontal: 4, vertical: 1),
                          decoration: BoxDecoration(
                            color: Colors.red,
                            borderRadius: BorderRadius.only(topLeft: Radius.circular(4.0), topRight: Radius.circular(4.0), bottomRight: Radius.circular(4.0)),
                          ),
                          child: Text(
                            "必得体验金",
                            style: TextStyle(
                              color: Colors.white,
                              fontSize: 10,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                        ),
                      )
                    ],
                  );
                }
            );
          } else if (value.isEqualTo("继续")) {
            return Stack(
              alignment: Alignment.center,
              clipBehavior: Clip.none,
              children: [
                _buildDownloadBtn(
                    onDownload: () async {
                      final decision = await DownloadProvider().handleDownloadWithDecision(game.downloadInfo);
                      if (decision == DownloadDecision.requiresConfirmation && context.mounted) {
                        CommonAlert.show(
                            context: context,
                            title: "温馨提示",
                            content: "使用蜂窝数据下载【${game.tGidName}】官方包",
                            cancelText: "稍后用WiFi下载",
                            confirmText: "确定下载",
                            onCancel: () async {
                              await DownloadProvider().scheduleDownloadOnWifi(game.downloadInfo);
                            },
                            onConfirm: () async {
                              await DownloadProvider().handleDownload(game.downloadInfo);
                            }
                        );
                      } else if (decision == DownloadDecision.noNetwork) {
                        Fluttertoast.showToast(
                          msg: '当前无网络连接，请检查网络后重试',
                          toastLength: Toast.LENGTH_SHORT,
                          gravity: ToastGravity.CENTER,
                        );
                      } else if (decision == DownloadDecision.unknown) {
                        Fluttertoast.showToast(
                          msg: '网络异常，请稍后重试',
                          toastLength: Toast.LENGTH_SHORT,
                          gravity: ToastGravity.CENTER,
                        );
                      }
                    },
                    child: Container(
                      decoration: ShapeDecoration(
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(6),
                        ),
                      ),
                      clipBehavior: Clip.antiAlias,
                      child: Stack(
                        children: [
                          LinearProgressIndicator(
                            value: (game.downloadInfo.cacheProgress ?? 0.0) / 100,
                            backgroundColor: DLColor.downloadBackground,
                            valueColor: const AlwaysStoppedAnimation<Color>(DLColor.primary),
                            minHeight: 44.0,
                          ),
                          Center(
                            child: Text(
                              value,
                              style: TextStyle(
                                color: Colors.white,
                              ),
                            ),
                          )
                        ],
                      ),
                    )
                ),
                Positioned(
                  top: -10,
                  right: -10,
                  child: Container(
                    padding: EdgeInsets.symmetric(horizontal: 4, vertical: 1),
                    decoration: BoxDecoration(
                      color: Colors.red,
                      borderRadius: BorderRadius.only(topLeft: Radius.circular(4.0), topRight: Radius.circular(4.0), bottomRight: Radius.circular(4.0)),
                    ),
                    child: Text(
                      "必得体验金",
                      style: TextStyle(
                        color: Colors.white,
                        fontSize: 10,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ),
                )
              ],
            );
          } else {
            return Stack(
              alignment: Alignment.center,
              clipBehavior: Clip.none,
              children: [
                _buildDownloadBtn(
                    onDownload: () async {
                      if (game.downloadInfo.url.isEmpty) {
                        // 找兜底数据
                        final pocketGame = GameDataManager().findPocketGame(game.tGid);
                        if (pocketGame != null) {
                          DownloadInfo downloadInfo = DownloadInfo(name: pocketGame.tGidName, url: pocketGame.downloadInfo.url);
                          game.downloadInfo = downloadInfo;
                          GameDataManager().refreshGamesDownloadInfo(game.tGid, downloadInfo);
                        }
                      }
                      final decision = await DownloadProvider().handleDownloadWithDecision(game.downloadInfo);
                      if (decision == DownloadDecision.requiresConfirmation && context.mounted) {
                        CommonAlert.show(
                            context: context,
                            title: "温馨提示",
                            content: "使用蜂窝数据下载【${game.tGidName}】官方包",
                            cancelText: "稍后用WiFi下载",
                            confirmText: "确定下载",
                            onCancel: () async {
                              await DownloadProvider().scheduleDownloadOnWifi(game.downloadInfo);
                            },
                            onConfirm: () async {
                              await DownloadProvider().handleDownload(game.downloadInfo);
                            }
                        );
                      } else if (decision == DownloadDecision.noNetwork) {
                        Fluttertoast.showToast(
                          msg: '当前无网络连接，请检查网络后重试',
                          toastLength: Toast.LENGTH_SHORT,
                          gravity: ToastGravity.CENTER,
                        );
                      } else if (decision == DownloadDecision.unknown) {
                        Fluttertoast.showToast(
                          msg: '下载链接无效，请稍后重试',
                          toastLength: Toast.LENGTH_SHORT,
                          gravity: ToastGravity.CENTER,
                        );
                      }
                    },
                    child: Text(
                      value.isEqualTo("下载") ? "下载官方包${game.downloadInfo.size ?? ''}" : value,
                      style: TextStyle(
                        color: Colors.white,
                      ),
                    )
                ),
                if (game.floatConfig.floatMsg) ...[
                  Positioned(
                    top: -10,
                    right: -10,
                    child: Container(
                      padding: EdgeInsets.symmetric(horizontal: 4, vertical: 1),
                      decoration: BoxDecoration(
                        color: Colors.red,
                        borderRadius: BorderRadius.only(topLeft: Radius.circular(4.0), topRight: Radius.circular(4.0), bottomRight: Radius.circular(4.0)),
                      ),
                      child: Text(
                        "必得体验金",
                        style: TextStyle(
                          color: Colors.white,
                          fontSize: 10,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                    ),
                  )
                ]
              ],
            );
          }
        }
    );
  }

  /// 普通下载按钮
  Widget _buildDownloadBtn({required void Function() onDownload, required Widget child}) {
    return SizedBox(
      width: double.infinity,
      height: 44,
      child: ElevatedButton(
        style: ElevatedButton.styleFrom(
          backgroundColor: DLColor.primary,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(6),
          ),
          padding: EdgeInsets.zero,
          tapTargetSize: MaterialTapTargetSize.shrinkWrap,
          side: BorderSide.none,
        ),
        onPressed: onDownload,
        child: child,
      )
    );
  }
}