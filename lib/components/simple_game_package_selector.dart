import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'dart:io';
import '../../providers/game_circle_provider.dart';
import '../../config/app_config.dart';
import '../../services/bind_account_service.dart';
import '../../model/package_info.dart';
import '../../utils/log_util.dart';

/// 简化的游戏包体选择组件（用于弹窗）
class SimpleGamePackageSelector extends StatefulWidget {
  final Function(PackageInfoItem)? onPackageSelected;
  
  const SimpleGamePackageSelector({
    super.key,
    this.onPackageSelected,
  });

  @override
  State<SimpleGamePackageSelector> createState() => _SimpleGamePackageSelectorState();
}

class _SimpleGamePackageSelectorState extends State<SimpleGamePackageSelector> {
  List<PackageInfoItem> _packageList = [];
  bool _isLoading = true;
  String? _error;
  int _selectedIndex = -1;

  @override
  void initState() {
    super.initState();
    _loadGamePackages();
  }

  /// Load game packages
  Future<void> _loadGamePackages() async {
    try {
      setState(() {
        _isLoading = true;
        _error = null;
      });

      final gameCircleProvider = Provider.of<GameCircleProvider>(
        context,
        listen: false,
      );

      if (!gameCircleProvider.hasSelectedGameCircle ||
          gameCircleProvider.selectedGameCircle == null) {
        throw Exception('No selected game circle');
      }

      final selectedCircle = gameCircleProvider.selectedGameCircle!;
      final tgid = selectedCircle.tgid;

      LogUtil.d('Starting to get game package info, tgid: $tgid');

      // Call bind account service API to get package info
      final bindAccountService = BindAccountService();
      final response = await bindAccountService.getPackageInfo(
        pid: AppConfig.pid,
        gid: AppConfig.gid,
        tgid: tgid,
        os: Platform.isAndroid ? 'android' : 'ios',
        packageNameList: [], // Empty list means get all available packages
      );

      if (response.success && response.data?.data?.packageInfo != null) {
        final packages = response.data!.data!.packageInfo;
        LogUtil.d('Game package info retrieved successfully, ${packages.length} packages');
        
        if (mounted) {
          setState(() {
            _packageList = packages;
            _isLoading = false;
            // 默认选择第一个包
            if (packages.isNotEmpty) {
              _selectedIndex = 0;
              widget.onPackageSelected?.call(packages[0]);
            }
          });
        }
      } else {
        LogUtil.w('Failed to get game package info: ${response.message}');
        throw Exception(response.message);
      }
    } catch (e) {
      LogUtil.e('Exception loading game package info: $e');
      if (mounted) {
        setState(() {
          _error = e.toString();
          _isLoading = false;
        });
      }
    }
  }

  void _selectPackage(int index) {
    setState(() {
      _selectedIndex = index;
    });
    if (index >= 0 && index < _packageList.length) {
      widget.onPackageSelected?.call(_packageList[index]);
    }
  }

  @override
  Widget build(BuildContext context) {
    if (_isLoading) {
      return const SizedBox(
        height: 100,
        child: Center(
          child: CircularProgressIndicator(),
        ),
      );
    }

    if (_error != null) {
      return SizedBox(
        height: 100,
        child: Center(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              const Icon(Icons.error_outline, color: Colors.grey),
              const SizedBox(height: 8),
              Text(
                '加载失败',
                style: TextStyle(color: Colors.grey[600], fontSize: 14),
              ),
            ],
          ),
        ),
      );
    }

    if (_packageList.isEmpty) {
      return const SizedBox(
        height: 100,
        child: Center(
          child: Text(
            '暂无可用的游戏包',
            style: TextStyle(color: Colors.grey, fontSize: 14),
          ),
        ),
      );
    }

    return Container(
      padding: const EdgeInsets.symmetric(vertical: 20),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceEvenly,
        children: List.generate(_packageList.length, (index) {
          final packageInfo = _packageList[index];
          final isSelected = _selectedIndex == index;
          
          return GestureDetector(
            onTap: () => _selectPackage(index),
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                Stack(
                  clipBehavior: Clip.none, // 允许子元素超出边界
                  children: [
                    Container(
                      width: 80,
                      height: 80,
                      decoration: BoxDecoration(
                        borderRadius: BorderRadius.circular(16),
                        border: Border.all(
                          color: isSelected ? const Color.fromRGBO(69, 113, 251, 1) : Colors.transparent,
                          width: 3,
                        ),
                      ),
                      child: ClipRRect(
                        borderRadius: BorderRadius.circular(13),
                        child: packageInfo.iconUrl.isNotEmpty
                            ? Image.network(
                                packageInfo.iconUrl,
                                fit: BoxFit.cover,
                                errorBuilder: (context, error, stackTrace) {
                                  return Container(
                                    color: Colors.grey[200],
                                    child: const Icon(
                                      Icons.games,
                                      size: 40,
                                      color: Colors.grey,
                                    ),
                                  );
                                },
                              )
                            : Container(
                                color: Colors.grey[200],
                                child: const Icon(
                                  Icons.games,
                                  size: 40,
                                  color: Colors.grey,
                                ),
                              ),
                      ),
                    ),
                    // 官方标识 - 位置在右上角，超出选中框
                    if (packageInfo.packageType.toLowerCase() == 'official')
                      Positioned(
                        top: -15,
                        right: -20,
                        child: Image.asset(
                          'assets/images/official.png',
                          width: 40,
                          height: 40,
                          errorBuilder: (context, error, stackTrace) {
                            return Container(
                              width: 40,
                              height: 40,
                              decoration: const BoxDecoration(
                                color: Colors.red,
                                shape: BoxShape.circle,
                              ),
                              child: const Center(
                                child: Text(
                                  '官',
                                  style: TextStyle(
                                    color: Colors.white,
                                    fontSize: 20,
                                    fontWeight: FontWeight.bold,
                                  ),
                                ),
                              ),
                            );
                          },
                        ),
                      ),
                  ],
                ),
                  
                const SizedBox(height: 8),
                packageInfo.packageType.toLowerCase() != 'official'
                    ? Text(
                        _getPackageTypeDescription(packageInfo.packageType),
                        style: TextStyle(
                          fontSize: 12,
                          color: isSelected ? const Color.fromRGBO(69, 113, 251, 1) : Colors.grey[600],
                          fontWeight: isSelected ? FontWeight.w500 : FontWeight.normal,
                        ),
                      )
                    : const SizedBox(height: 18), // 保持高度一致的占位空间
              ],
            ),
          );
        }),
      ),
    );
  }

  /// Get package type description
  String _getPackageTypeDescription(String packageType) {
    switch (packageType.toLowerCase()) {
      case 'official':
        return '官方包';
      case 'wechat':
        return '微信小游戏';
      case 'other_package':
        return '其他包体';
      default:
        return packageType;
    }
  }
}