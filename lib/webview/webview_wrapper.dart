import 'dart:io';
import 'package:flutter/material.dart';
import 'package:webview_flutter/webview_flutter.dart';
import 'js_bridge.dart';

/// WebView封装组件
class WebViewWrapper extends StatefulWidget {
  /// 初始加载的URL
  final String? initialUrl;
  
  /// 初始加载的HTML内容
  final String? initialHtml;
  
  /// JavaScript通信桥接器
  final JSBridge? jsBridge;
  
  /// 页面加载完成回调
  final VoidCallback? onPageFinished;
  
  /// 页面开始加载回调
  final VoidCallback? onPageStarted;
  
  /// 导航决策回调
  final Future<NavigationDecision> Function(NavigationRequest)? onNavigationRequest;
  
  /// 是否启用JavaScript，默认为true
  final bool enableJavaScript;
  
  /// 是否启用DOM存储，默认为true
  final bool enableDomStorage;
  
  /// 用户代理字符串
  final String? userAgent;

  /// WebView背景颜色
  final Color? backgroundColor;

  /// WebView控制器
  final WebViewWrapperController? controller;

  const WebViewWrapper({
    Key? key,
    this.initialUrl,
    this.initialHtml,
    this.jsBridge,
    this.onPageFinished,
    this.onPageStarted,
    this.onNavigationRequest,
    this.enableJavaScript = true,
    this.enableDomStorage = true,
    this.userAgent,
    this.backgroundColor,
    this.controller,
  }) : assert(initialUrl != null || initialHtml != null, '必须提供initialUrl或initialHtml中的一个'),
       super(key: key);

  @override
  State<WebViewWrapper> createState() => _WebViewWrapperState();
}

/// WebViewWrapper的控制器，用于在外部调用WebView方法
class WebViewWrapperController {
  _WebViewWrapperState? _state;

  void _attachState(_WebViewWrapperState state) {
    _state = state;
  }

  void _detachState() {
    _state = null;
  }

  /// 执行JavaScript代码
  Future<void> executeJavaScript(String script) async {
    await _state?.executeJavaScript(script);
  }

  /// 向WebView发送消息
  Future<void> sendMessageToWebView(Map<String, dynamic> message) async {
    await _state?.sendMessageToWebView(message);
  }

  /// 重新加载页面
  Future<void> reload() async {
    await _state?.reload();
  }

  /// 返回上一页
  Future<void> goBack() async {
    await _state?.goBack();
  }

  /// 前进到下一页
  Future<void> goForward() async {
    await _state?.goForward();
  }

  /// 获取当前URL
  Future<String?> getCurrentUrl() async {
    return await _state?.getCurrentUrl();
  }
}

class _WebViewWrapperState extends State<WebViewWrapper> {
  WebViewController? _controller;
  bool _hasError = false;
  String _errorMessage = '';

  @override
  void initState() {
    super.initState();
    widget.controller?._attachState(this);
    _initializePlatform();
  }

  void _initializePlatform() {
    // 针对Android平台进行特殊处理
    if (Platform.isAndroid) {
      // 延迟初始化，确保平台准备完成
      WidgetsBinding.instance.addPostFrameCallback((_) {
        _initializeController();
      });
    } else {
      _initializeController();
    }
  }

  @override
  void dispose() {
    widget.controller?._detachState();
    super.dispose();
  }

  void _initializeController() {
    try {
      _controller = WebViewController()
        ..setJavaScriptMode(JavaScriptMode.unrestricted)
        ..setBackgroundColor(widget.backgroundColor ?? Colors.transparent)
        ..addJavaScriptChannel(
            'FlutterJSBridge',
            onMessageReceived: (JavaScriptMessage message) {
              widget.jsBridge!.handleMessage(message.message);
        })
        ..setNavigationDelegate(
          NavigationDelegate(
            onPageStarted: (String url) {
              widget.onPageStarted?.call();
            },
            onPageFinished: (String url) {
              // 延迟注入JS桥接代码，确保页面完全加载
              if (widget.jsBridge != null) {
                Future.delayed(const Duration(milliseconds: 500), () {
                  _injectJSBridge();
                });
              }
              
              widget.onPageFinished?.call();
            },
            onWebResourceError: (WebResourceError error) {
              if (mounted) {
                //todo 暂时屏蔽webview的资源报错提示
              }
            },
            onNavigationRequest: (NavigationRequest request) async {
              if (widget.onNavigationRequest != null) {
                return await widget.onNavigationRequest!(request);
              }
              return NavigationDecision.navigate;
            },
          ),
        );

      // 设置用户代理
      if (widget.userAgent != null) {
        _controller!.setUserAgent(widget.userAgent!);
      }

      // 加载内容
      if (widget.initialUrl != null) {
        _controller!.loadRequest(Uri.parse(widget.initialUrl!));
      } else if (widget.initialHtml != null) {
        _controller!.loadHtmlString(widget.initialHtml!);
      }

      // 通知Widget重建
      if (mounted) {
        setState(() {});
      }

    } catch (e) {
      if (mounted) {
        setState(() {
          _hasError = true;
          _errorMessage = 'WebView初始化失败: $e';
        });
      }
    }
  }

  /// 注入JavaScript桥接代码
  void _injectJSBridge() {
    if (widget.jsBridge == null || _controller == null) return;

    try {
      // 首先添加JavaScript通道
      // _controller?.addJavaScriptChannel(
      //   'FlutterJSBridge',
      //   onMessageReceived: (JavaScriptMessage message) {
      //     widget.jsBridge!.handleMessage(message.message);
      //   },
      // );

      // final bridgeScript = '''
      //   (function() {
      //     // 确保只初始化一次
      //     if (window.FlutterJSBridge) {
      //       return;
      //     }
      //
      //     window.FlutterJSBridge = {
      //       postMessage: function(data) {
      //         try {
      //           window.FlutterJSBridge.postMessage(JSON.stringify(data));
      //         } catch(e) {
      //           console.error('Error posting message to Flutter:', e);
      //         }
      //       },
      //
      //       // 兼容老版本的接口
      //       callFlutter: function(method, data) {
      //         this.postMessage({
      //           method: method,
      //           data: data
      //         });
      //       }
      //     };
      //
      //     // 通知Flutter页面已准备好
      //     setTimeout(function() {
      //       if (window.FlutterJSBridge) {
      //         window.FlutterJSBridge.postMessage({
      //           type: 'ready',
      //           method: 'ready',
      //           data: 'WebView已准备好接收消息'
      //         });
      //       }
      //     }, 200);
      //   })();
      // ''';
      //
      // _controller!.runJavaScript(bridgeScript);
    } catch (e) {
      debugPrint('注入JS桥接代码失败: $e');
    }
  }

  /// 执行JavaScript代码
  Future<void> executeJavaScript(String script) async {
    if (_controller != null) {
      await _controller!.runJavaScript(script);
    }
  }

  /// 向WebView发送消息
  Future<void> sendMessageToWebView(Map<String, dynamic> message) async {
    if (_controller == null) return;
    
    final messageJson = message.toString().replaceAll("'", '"');
    final script = '''
      if (window.onFlutterMessage && typeof window.onFlutterMessage === 'function') {
        try {
          window.onFlutterMessage($messageJson);
        } catch(e) {
          console.error('Error calling onFlutterMessage:', e);
        }
      }
    ''';
    await executeJavaScript(script);
  }

  /// 重新加载页面
  Future<void> reload() async {
    if (_controller != null) {
      await _controller!.reload();
    }
  }

  /// 返回上一页
  Future<void> goBack() async {
    if (_controller != null && await _controller!.canGoBack()) {
      await _controller!.goBack();
    }
  }

  /// 前进到下一页
  Future<void> goForward() async {
    if (_controller != null && await _controller!.canGoForward()) {
      await _controller!.goForward();
    }
  }

  /// 获取当前URL
  Future<String?> getCurrentUrl() async {
    if (_controller != null) {
      return await _controller!.currentUrl();
    }
    return null;
  }

  @override
  Widget build(BuildContext context) {
    if (_hasError) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            const Icon(
              Icons.error_outline,
              size: 64,
              color: Colors.red,
            ),
            const SizedBox(height: 16),
            Text(
              'WebView加载失败',
              style: Theme.of(context).textTheme.headlineSmall,
            ),
            const SizedBox(height: 8),
            Padding(
              padding: const EdgeInsets.symmetric(horizontal: 32),
              child: Text(
                _errorMessage,
                textAlign: TextAlign.center,
                style: Theme.of(context).textTheme.bodyMedium,
              ),
            ),
            const SizedBox(height: 16),
            ElevatedButton(
              onPressed: () {
                setState(() {
                  _hasError = false;
                });
                _initializeController();
              },
              child: const Text('重试'),
            ),
          ],
        ),
      );
    }

    if (_controller == null) {
      return const SizedBox.shrink();
    }

    return WebViewWidget(controller: _controller!);
  }
} 