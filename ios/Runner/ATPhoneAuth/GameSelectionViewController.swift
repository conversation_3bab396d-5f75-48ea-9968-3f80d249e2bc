//
//  GameSelectionViewController.swift
//  Runner
//
//  Created by Claude Code on 2025/8/23.
//
import UIKit

/// 游戏选择弹窗视图控制器
class GameSelectionViewController: UIViewController {
    
    /// 游戏数据
    private var gameList: [[String: Any]] = []
    
    /// 选择游戏的回调
    var onGameSelected: (([String: Any]) -> Void)?
    
    /// 取消回调
    var onCancel: (() -> Void)?
    
    /// 容器视图
    private var containerView: UIView!
    
    /// 拖拽指示器
    private var dragIndicator: UIView!
    
    /// 标题标签
    private var titleLabel: UILabel!
    
    /// 集合视图
    private var collectionView: UICollectionView!
    
    /// 初始化方法
    /// - Parameter gameList: 游戏数据列表
    init(gameList: [[String: Any]]) {
        self.gameList = gameList
        super.init(nibName: nil, bundle: nil)
        self.modalPresentationStyle = .overFullScreen
        self.modalTransitionStyle = .crossDissolve
    }
    
    required init?(coder: NSCoder) {
        fatalError("init(coder:) has not been implemented")
    }
    
    override func viewDidLoad() {
        super.viewDidLoad()
        
        // 调试：打印游戏数据
        print("GameSelectionViewController - 游戏数据数量: \(gameList.count)")
        for (index, game) in gameList.enumerated() {
            print("游戏 \(index): \(game)")
        }
        
        setupUI()
        setupGestures()
    }
    
    override func viewWillAppear(_ animated: Bool) {
        super.viewWillAppear(animated)
        animateIn()
    }
    
    /// 设置UI界面
    private func setupUI() {
        // 半透明背景
        view.backgroundColor = UIColor.black.withAlphaComponent(0.4)
        
        // 容器视图 - 底部弹窗样式
        containerView = UIView()
        containerView.backgroundColor = UIColor.white
        containerView.layer.cornerRadius = 20
        containerView.layer.maskedCorners = [.layerMinXMinYCorner, .layerMaxXMinYCorner] // 只有顶部圆角
        containerView.layer.masksToBounds = true
        containerView.translatesAutoresizingMaskIntoConstraints = false
        view.addSubview(containerView)
        
        // 拖拽指示器
        dragIndicator = UIView()
        dragIndicator.backgroundColor = UIColor.lightGray
        dragIndicator.layer.cornerRadius = 2.5
        dragIndicator.translatesAutoresizingMaskIntoConstraints = false
        containerView.addSubview(dragIndicator)
        
        // 标题
        titleLabel = UILabel()
        titleLabel.text = "请选择要登录的微信小游戏"
        titleLabel.font = UIFont.systemFont(ofSize: 16)
        titleLabel.textColor = UIColor.black
        titleLabel.textAlignment = .center
        titleLabel.translatesAutoresizingMaskIntoConstraints = false
        containerView.addSubview(titleLabel)
        
        // 集合视图布局 - 水平滚动
        let layout = UICollectionViewFlowLayout()
        layout.scrollDirection = .horizontal
        layout.minimumLineSpacing = 15
        layout.minimumInteritemSpacing = 0
        layout.sectionInset = UIEdgeInsets(top: 20, left: 20, bottom: 20, right: 20)
        
        // 计算item大小 (水平排列，每个item显示一个游戏)
        let itemWidth: CGFloat = 100
        let itemHeight: CGFloat = 130 // 100图标 + 30文字
        layout.itemSize = CGSize(width: itemWidth, height: itemHeight)
        
        // 集合视图
        collectionView = UICollectionView(frame: .zero, collectionViewLayout: layout)
        collectionView.backgroundColor = UIColor.clear
        collectionView.delegate = self
        collectionView.dataSource = self
        collectionView.register(GameCollectionViewCell.self, forCellWithReuseIdentifier: "GameCell")
        collectionView.showsVerticalScrollIndicator = false
        collectionView.isUserInteractionEnabled = true // 确保可以交互
        collectionView.translatesAutoresizingMaskIntoConstraints = false
        containerView.addSubview(collectionView)
        
        // 约束
        NSLayoutConstraint.activate([
            // 容器视图约束 - 底部对齐
            containerView.leadingAnchor.constraint(equalTo: view.leadingAnchor),
            containerView.trailingAnchor.constraint(equalTo: view.trailingAnchor),
            containerView.bottomAnchor.constraint(equalTo: view.bottomAnchor),
            containerView.heightAnchor.constraint(equalToConstant: 280), // 固定高度
            
            // 拖拽指示器约束
            dragIndicator.topAnchor.constraint(equalTo: containerView.topAnchor, constant: 8),
            dragIndicator.centerXAnchor.constraint(equalTo: containerView.centerXAnchor),
            dragIndicator.widthAnchor.constraint(equalToConstant: 40),
            dragIndicator.heightAnchor.constraint(equalToConstant: 5),
            
            // 标题约束
            titleLabel.topAnchor.constraint(equalTo: dragIndicator.bottomAnchor, constant: 20),
            titleLabel.leadingAnchor.constraint(equalTo: containerView.leadingAnchor, constant: 20),
            titleLabel.trailingAnchor.constraint(equalTo: containerView.trailingAnchor, constant: -20),
            
            // 集合视图约束
            collectionView.topAnchor.constraint(equalTo: titleLabel.bottomAnchor, constant: 20),
            collectionView.leadingAnchor.constraint(equalTo: containerView.leadingAnchor),
            collectionView.trailingAnchor.constraint(equalTo: containerView.trailingAnchor),
            collectionView.bottomAnchor.constraint(equalTo: containerView.bottomAnchor, constant: -20)
        ])
    }
    
    /// 设置手势
    private func setupGestures() {
        // 点击背景关闭
        let tapGesture = UITapGestureRecognizer(target: self, action: #selector(backgroundTapped))
        view.addGestureRecognizer(tapGesture)
        
        // 下拉关闭手势 - 只在拖拽指示器上添加
        let panGesture = UIPanGestureRecognizer(target: self, action: #selector(handlePan(_:)))
        dragIndicator.addGestureRecognizer(panGesture)
    }
    
    /// 背景点击
    @objc private func backgroundTapped(_ gesture: UITapGestureRecognizer) {
        let location = gesture.location(in: view)
        if !containerView.frame.contains(location) {
            dismissWithCancel()
        }
    }
    
    /// 处理拖拽手势
    @objc private func handlePan(_ gesture: UIPanGestureRecognizer) {
        let translation = gesture.translation(in: view)
        let velocity = gesture.velocity(in: view)
        
        switch gesture.state {
        case .changed:
            if translation.y > 0 { // 只允许向下拖拽
                containerView.transform = CGAffineTransform(translationX: 0, y: translation.y)
            }
        case .ended:
            // 如果拖拽距离或速度足够大，则关闭弹窗
            if translation.y > 100 || velocity.y > 500 {
                dismissWithCancel()
            } else {
                // 否则弹回原位
                UIView.animate(withDuration: 0.3, delay: 0, usingSpringWithDamping: 0.8, initialSpringVelocity: 0, options: [], animations: {
                    self.containerView.transform = .identity
                }, completion: nil)
            }
        default:
            break
        }
    }
    
    /// 入场动画 - 从底部滑入
    private func animateIn() {
        containerView.transform = CGAffineTransform(translationX: 0, y: containerView.frame.height)
        
        UIView.animate(withDuration: 0.4, delay: 0, usingSpringWithDamping: 0.8, initialSpringVelocity: 0, options: [], animations: {
            self.containerView.transform = .identity
        }, completion: nil)
    }
    
    /// 出场动画 - 滑出到底部
    private func animateOut(completion: @escaping () -> Void) {
        UIView.animate(withDuration: 0.3, animations: {
            self.containerView.transform = CGAffineTransform(translationX: 0, y: self.containerView.frame.height)
            self.view.backgroundColor = UIColor.clear
        }) { _ in
            completion()
        }
    }
    
    /// 取消并关闭弹窗
    private func dismissWithCancel() {
        animateOut {
            self.dismiss(animated: false) {
                self.onCancel?()
            }
        }
    }
}

// MARK: - UICollectionViewDataSource & UICollectionViewDelegate
extension GameSelectionViewController: UICollectionViewDataSource, UICollectionViewDelegate {
    
    func collectionView(_ collectionView: UICollectionView, numberOfItemsInSection section: Int) -> Int {
        print("CollectionView numberOfItemsInSection: \(gameList.count)")
        return gameList.count
    }
    
    func collectionView(_ collectionView: UICollectionView, cellForItemAt indexPath: IndexPath) -> UICollectionViewCell {
        print("CollectionView cellForItemAt: \(indexPath.item)")
        let cell = collectionView.dequeueReusableCell(withReuseIdentifier: "GameCell", for: indexPath) as! GameCollectionViewCell
        let gameData = gameList[indexPath.item]
        
        // 添加点击手势作为备用
        let tapGesture = UITapGestureRecognizer(target: self, action: #selector(cellTapped(_:)))
        cell.addGestureRecognizer(tapGesture)
        cell.tag = indexPath.item
        
        cell.configure(with: gameData)
        return cell
    }
    
    @objc private func cellTapped(_ gesture: UITapGestureRecognizer) {
        if let cell = gesture.view {
            let indexPath = IndexPath(item: cell.tag, section: 0)
            print("Cell 手势点击被调用: \(indexPath.item)")
            collectionView(collectionView, didSelectItemAt: indexPath)
        }
    }
    
    func collectionView(_ collectionView: UICollectionView, didSelectItemAt indexPath: IndexPath) {
        print("CollectionView didSelectItemAt被调用: \(indexPath.item)")
        let selectedGame = gameList[indexPath.item]
        print("CollectionView 选择了游戏: \(selectedGame)")
        animateOut {
            self.dismiss(animated: false) {
                self.onGameSelected?(selectedGame)
            }
        }
    }
}

// MARK: - 游戏集合视图单元格
class GameCollectionViewCell: UICollectionViewCell {
    
    /// 游戏图标
    private var iconImageView: UIImageView!
    
    /// 游戏名称
    private var nameLabel: UILabel!
    
    /// 加载指示器
    private var activityIndicator: UIActivityIndicatorView!
    
    override init(frame: CGRect) {
        super.init(frame: frame)
        setupUI()
    }
    
    required init?(coder: NSCoder) {
        fatalError("init(coder:) has not been implemented")
    }
    
    private func setupUI() {
        // 图标视图 - 圆形
        iconImageView = UIImageView()
        iconImageView.contentMode = .scaleAspectFill
        iconImageView.backgroundColor = UIColor.lightGray
        iconImageView.translatesAutoresizingMaskIntoConstraints = false
        contentView.addSubview(iconImageView)
        
        // 加载指示器
        if #available(iOS 13.0, *) {
            activityIndicator = UIActivityIndicatorView(style: .medium)
        } else {
            activityIndicator = UIActivityIndicatorView(style: .gray)
        }
        activityIndicator.translatesAutoresizingMaskIntoConstraints = false
        contentView.addSubview(activityIndicator)
        
        // 名称标签
        nameLabel = UILabel()
        nameLabel.font = UIFont.systemFont(ofSize: 14)
        nameLabel.textColor = UIColor.black
        nameLabel.textAlignment = .center
        nameLabel.numberOfLines = 1
        nameLabel.translatesAutoresizingMaskIntoConstraints = false
        contentView.addSubview(nameLabel)
        
        // 约束
        NSLayoutConstraint.activate([
            // 图标约束 - 圆形，宽高相等
            iconImageView.topAnchor.constraint(equalTo: contentView.topAnchor),
            iconImageView.centerXAnchor.constraint(equalTo: contentView.centerXAnchor),
            iconImageView.widthAnchor.constraint(equalToConstant: 80),
            iconImageView.heightAnchor.constraint(equalToConstant: 80),
            
            // 加载指示器约束
            activityIndicator.centerXAnchor.constraint(equalTo: iconImageView.centerXAnchor),
            activityIndicator.centerYAnchor.constraint(equalTo: iconImageView.centerYAnchor),
            
            // 名称约束
            nameLabel.topAnchor.constraint(equalTo: iconImageView.bottomAnchor, constant: 8),
            nameLabel.leadingAnchor.constraint(equalTo: contentView.leadingAnchor),
            nameLabel.trailingAnchor.constraint(equalTo: contentView.trailingAnchor),
            nameLabel.bottomAnchor.constraint(equalTo: contentView.bottomAnchor)
        ])
    }
    
    override func layoutSubviews() {
        super.layoutSubviews()
        // 设置圆形头像
        iconImageView.layer.cornerRadius = iconImageView.frame.width / 2
        iconImageView.layer.masksToBounds = true
    }
    
    /// 配置单元格数据
    /// - Parameter gameData: 游戏数据
    func configure(with gameData: [String: Any]) {
        // 调试：打印单元格数据
        print("GameCollectionViewCell - 配置数据: \(gameData)")
        
        // 设置游戏名称
        if let name = gameData["name"] as? String {
            nameLabel.text = name
            print("设置游戏名称: \(name)")
        } else {
            nameLabel.text = "未知游戏"
            print("未找到游戏名称，设置为默认值")
        }
        
        // 设置游戏图标
        if let iconUrl = gameData["icon"] as? String, !iconUrl.isEmpty {
            print("开始加载图片: \(iconUrl)")
            if let url = URL(string: iconUrl) {
                loadImage(from: url)
            } else {
                print("URL格式错误: \(iconUrl)")
                iconImageView.image = nil
                iconImageView.backgroundColor = UIColor.lightGray
            }
        } else {
            print("图片URL为空或不存在")
            iconImageView.image = nil
            iconImageView.backgroundColor = UIColor.lightGray
        }
    }
    
    /// 加载网络图片
    /// - Parameter url: 图片URL
    private func loadImage(from url: URL) {
        print("开始网络请求图片: \(url)")
        activityIndicator.startAnimating()
        iconImageView.image = nil
        iconImageView.backgroundColor = UIColor.lightGray
        
        URLSession.shared.dataTask(with: url) { [weak self] data, response, error in
            DispatchQueue.main.async {
                self?.activityIndicator.stopAnimating()
                
                if let error = error {
                    print("图片加载失败: \(error.localizedDescription)")
                    self?.iconImageView.backgroundColor = UIColor.red.withAlphaComponent(0.3) // 使用红色背景表示加载失败
                    return
                }
                
                if let data = data, let image = UIImage(data: data) {
                    print("图片加载成功，大小: \(image.size)")
                    self?.iconImageView.image = image
                    self?.iconImageView.backgroundColor = UIColor.clear
                } else {
                    print("图片数据解析失败")
                    self?.iconImageView.backgroundColor = UIColor.orange.withAlphaComponent(0.3) // 使用橙色背景表示数据解析失败
                }
            }
        }.resume()
    }
}
