//
//  ATPhoneAuth.swift
//  RedCrystal
//
//  Created by <PERSON><PERSON> on 05/07/2021.
//  Copyright © 2021 jiaruh. All rights reserved.
//

import Foundation
import ATAuthSDK
import UIKit

typealias ATCommonComplete = (NSDictionary) -> Void
typealias TokenHandler = (_ json: String) -> Void
typealias CloseHandler = () -> Void
typealias ATResponseComplete = ([String: Any]?, NSError?) -> Void

// 闪验
class ATPhoneAuth: NSObject {
    
    static let instance = ATPhoneAuth()
    
    func signOut() {
        
    }
    var policy: String?
    var agreement: String?
    var authAppList: [[String: Any]] = []
    var kDecrypt2KeyLengthFromGameKey = 16
    var complete: ATResponseComplete? = nil
    let pnsCodeError = [PNSCodeGetOperatorInfoFailed: "获取运营商配置信息失败",
        PNSCodeNoSIMCard: "未检测到sim卡",
        PNSCodeNoCellularNetwork: "蜂窝网络未开启或不稳定",
        PNSCodeUnknownOperator: "无法判运营商",
        PNSCodeUnknownError: "未知异常",
        PNSCodeGetTokenFailed: "获取token失败",
        PNSCodeGetMaskPhoneFailed: "预取号失败",
        PNSCodeInterfaceDemoted: "运营商维护升级，该功能不可用",
        PNSCodeInterfaceLimited: "运营商维护升级，该功能已达最大调用次数",
        PNSCodeInterfaceTimeout: "接口超时",
        PNSCodeDecodeAppInfoFailed: "AppID、Appkey解析失败",
        PNSCodeCarrierChanged: "运营商已切换",
        PNSCodeEnvCheckFail: "终端环境检测失败（终端不支持认证 / 终端检测参数错误）"]

    private(set) var active = false
    private(set) var onClickProtocol = false
    var isCanUseOneKey: Bool = true
    // 开始状态置为true，默认当前环境可以使用一键登录
    var isCanUseOneKeyLogin: Bool! {
        get {
            ATPhoneAuth.instance.isCanUseOneKey
        }
        set {
            ATPhoneAuth.instance.isCanUseOneKey = newValue
        }
    }
    private(set) var useOneKeyLoginError = ""
    let style = PNSBuildModelStyle.PNSBuildModelStyleAlertAutorotate
    private(set) var sub = UIViewController(nibName: nil, bundle: nil)
    
    var isShowGoback = false
    
//    static let shared = ATPhoneAuth()
    override init() {
        super.init()
    }

    // 获取闪验配置
    func configFlashVerify(_ secret: String, policy: String, agreement: String, complete: @escaping ATCommonComplete) {
        self.policy = policy
        self.agreement = agreement
        TXCommonHandler.sharedInstance().setAuthSDKInfo(secret) { (sdkInfo) in
            self.checkAndPrepareEnv()
            complete(sdkInfo as NSDictionary)
            if let resultCode = sdkInfo["resultCode"] as? String, resultCode != PNSCodeSuccess {
//                BugLess.report("闪验SDK初始化参数失败", message: "resultDic：\(sdkInfo)", actionType: .defaultError)
            }
        }
    }
    
    // 闪验Token验证
    public func verifyFlashToken(with gid: String = "", token: String = "", complete: @escaping ATCommonComplete) {
//        ATPhoneAuth.instance.flashController?.verifyFlash(token: token)
    }

    func verifyToken(_ token: String) {
        verifyFlashToken(token: token) { (callback) in
//            RCLog.debug(callback.description)
        }
    }

    func checkAndPrepareEnv() {
        // 环境检查，异步返回
        TXCommonHandler.sharedInstance().checkEnvAvailable(
            with: PNSAuthType.loginToken,
            complete: {[weak self] resultDic in
                self?.isCanUseOneKeyLogin = resultDic?["resultCode"] as? String == PNSCodeSuccess
                if self?.isCanUseOneKeyLogin == true {
                    TXCommonHandler.sharedInstance().accelerateLoginPage(withTimeout: 5.0, complete: { resultDic in
//                        RCLog.debug("为后面授权页拉起加速，加速结果：\(resultDic)")
                    })
                } else {
                    // 环境检查不支持闪验
                    let status = ATPhoneAuth.string(from: resultDic?["resultCode"])
                    self?.useOneKeyLoginError = "\(ATPhoneAuth.string(from: self?.pnsCodeError[status]))(\(status))"
//                    BugLess.report("当前环境检查不支持闪验", message: self?.useOneKeyLoginError ?? status, actionType: .defaultError)
                }
            }
        )
    }
    
    func oneKeyLogin(in controller: UIViewController, silent: Bool = false, topBannerImage: UIImage?, complete: @escaping ATResponseComplete) {
        self.complete = complete
        if isCanUseOneKeyLogin == false {
            let error = NSError(domain: "ATPhoneAuth", code: -1000, userInfo: [NSLocalizedDescriptionKey: "当前环境不支持一键登录"])
            self.complete?(nil, error)
            return
        }
        
        guard let agreement = self.agreement, let policy = self.policy else {
            let error = NSError(domain: "ATPhoneAuth", code: -1000, userInfo: [NSLocalizedDescriptionKey: "协议链接为空"])
            self.complete?(nil, error)
            return
        }
        
        let model = PNSBuildModelUtils.buildAlertMode(with: self, wechatLoginSelector: #selector(clickWechatLogin), accountLoginTarget: self, accountLoginSelector: #selector(accountLoginOnClick), otherLoginTarget: self, otherLoginSelector: #selector(otherLoginOnClick), topBannerImage: topBannerImage, agreement: agreement, policy: policy, authAppList: authAppList)
        
        model?.privacyOne = ["《用户协议》", agreement]
        model?.privacyTwo = ["《隐私政策》", policy]
        
        // 调节宽高 - 将协议页面放到底部
        model?.contentViewFrameBlock = { _, superViewSize, frame in
            return UIScreen.main.bounds
        }

        TXCommonHandler.sharedInstance().getLoginToken(
            withTimeout: 3.0,
            controller: controller,
            model: model) {[weak self] resultDic in
                // 处理闪验登录结果
                let resultCode = resultDic["resultCode"] as? String
                if resultCode == PNSCodeLoginControllerPresentSuccess {
                    self?.onClickProtocol = false
                }
                else if resultCode == PNSCodeSuccess,
                   let token = resultDic["token"] as? String {
                    // 登录成功，将token通过回调返回给Flutter端
                    self?.cancelOneKeyLogin()
                    self?.complete?(["clickType": "atPhoneAuth", "success": true, "token": token], nil)
                } else if resultCode == PNSCodeLoginControllerClickCheckBoxBtn {
                    self?.onClickProtocol = !(self?.onClickProtocol ?? false)
                } else if resultCode == PNSCodeLoginControllerClickLoginBtn && self?.onClickProtocol == false {
                     HUD.info("请先同意用户协议及隐私政策")
//                    DouluoManagerChannel.shared.sendFastLoginUIEvent(event: "show_protocol_click_tip")
                } else {
                    self?.cancelOneKeyLogin()
                }
                
            }
    }
    
    @objc func clickWechatLogin() {
        
        // 检查隐私协议是否已勾选
        if !onClickProtocol {
            HUD.info("请先同意用户协议及隐私政策")
            return
        }
        
        // 检查是否有游戏列表
        if authAppList.isEmpty {
            print("没有游戏列表，直接发送微信登录事件")
            // 没有游戏列表，直接发送微信登录事件
            self.cancelOneKeyLogin()
            DouluoManagerChannel.shared.sendFastLoginUIEvent(event: "wechat_clicked")
            return
        }
        
        print("有游戏列表，弹出游戏选择弹窗")
        // 有游戏列表，弹出游戏选择弹窗
        DispatchQueue.main.async { [weak self] in
            guard let self = self else { return }
            
            let gameSelectionVC = GameSelectionViewController(gameList: self.authAppList)
            
            // 设置游戏选择回调
            gameSelectionVC.onGameSelected = { [weak self] selectedGame in
                guard let self = self else { return }
                
                print("用户选择了游戏: \(selectedGame)")
                // 提取游戏信息
                let tgid = selectedGame["tgid"] as? String ?? ""
                let miniProgramId = selectedGame["wechat_minigram_id"] as? String ?? ""
                let gameName = selectedGame["name"] as? String ?? ""
                
                print("发送游戏选择事件 - tgid: \(tgid), miniProgramId: \(miniProgramId), gameName: \(gameName)")
                
                // 发送游戏选择事件到Flutter端
                DouluoManagerChannel.shared.sendFastLoginUIEvent(
                    event: "wechat_game_selected",
                    arguments: [
                        "game_title": gameName,
                        "mini_program_id": miniProgramId,
                        "tgid": tgid
                    ]
                )
                // 关闭闪验弹窗
                self.cancelOneKeyLogin()
            }
            
            // 设置取消回调
            gameSelectionVC.onCancel = {}
            
            // 获取最顶层的视图控制器
            if let topVC = self.getTopViewController() {
                print("在顶层控制器弹出游戏选择弹窗: \(topVC)")
                topVC.present(gameSelectionVC, animated: true, completion: nil)
            } else {
                print("获取顶层控制器失败")
            }
        }
    }
    
    @objc func accountLoginOnClick() {
        // 发送账号登录事件到Flutter端
        self.cancelOneKeyLogin()
        DouluoManagerChannel.shared.sendFastLoginUIEvent(event: "account_login_clicked")
    }
    
    @objc func otherLoginOnClick() {
        self.cancelOneKeyLogin()
        // 发送其他登录方式事件到Flutter端
        DouluoManagerChannel.shared.sendFastLoginUIEvent(event: "other_phone_clicked")
    }
    
    func reOneKeyLogin(in controller: UIViewController) {
        cancelOneKeyLogin()
//        self.oneKeyLogin(in: controller, silent: true)
        // 未初始化闪验Sdk时，重新初始化成功，再次调用一键登录
    }
    
    func cancelOneKeyLogin() {
        DispatchQueue.main.async {
            TXCommonHandler.sharedInstance().cancelLoginVC(animated: false, complete: nil)
        }
    }
    
    /// 获取最顶层的视图控制器
    private func getTopViewController() -> UIViewController? {
        guard let keyWindow = UIApplication.shared.keyWindow,
              let rootViewController = keyWindow.rootViewController else {
            return nil
        }
        
        return getTopViewController(from: rootViewController)
    }
    
    /// 递归获取最顶层的视图控制器
    private func getTopViewController(from viewController: UIViewController) -> UIViewController {
        if let presented = viewController.presentedViewController {
            return getTopViewController(from: presented)
        }
        
        if let navigationController = viewController as? UINavigationController {
            return getTopViewController(from: navigationController.visibleViewController ?? navigationController)
        }
        
        if let tabBarController = viewController as? UITabBarController {
            return getTopViewController(from: tabBarController.selectedViewController ?? tabBarController)
        }
        
        return viewController
    }
    
    /// 进行类型转换获取字符串类型值
    static func string(from value: Any?, defaultValue: String = "") -> String {
        if let str = value as? String {
            return str
        } else if let int = value as? Int {
            return int.description
        } else if let double = value as? Double {
            return double.description
        } else if let float = value as? Float {
            return float.description
        } else {
            return defaultValue
        }
    }
    
    /// 进行类型转换获取Int类型值
    static func int(from value: Any?) -> Int? {
        if let num = value as? Int {
            return Int(num)
        } else if let num = value as? String {
            return Int(num)
        } else {
            return nil
        }
    }
}

// MARK: - Simple HUD Implementation
class HUD {
    static func info(_ message: String) {
        DispatchQueue.main.async {
            showToast(message: message)
        }
    }
    
    private static func showToast(message: String) {
        guard let window = UIApplication.shared.keyWindow else { return }
        
        // Create toast container
        let toastContainer = UIView()
        toastContainer.backgroundColor = UIColor.black.withAlphaComponent(0.8)
        toastContainer.layer.cornerRadius = 8
        toastContainer.clipsToBounds = true
        
        // Create label
        let toastLabel = UILabel()
        toastLabel.textColor = UIColor.white
        toastLabel.textAlignment = .center
        toastLabel.font = UIFont.systemFont(ofSize: 16)
        toastLabel.text = message
        toastLabel.numberOfLines = 0
        
        // Add label to container
        toastContainer.addSubview(toastLabel)
        toastLabel.translatesAutoresizingMaskIntoConstraints = false
        NSLayoutConstraint.activate([
            toastLabel.leadingAnchor.constraint(equalTo: toastContainer.leadingAnchor, constant: 16),
            toastLabel.trailingAnchor.constraint(equalTo: toastContainer.trailingAnchor, constant: -16),
            toastLabel.topAnchor.constraint(equalTo: toastContainer.topAnchor, constant: 12),
            toastLabel.bottomAnchor.constraint(equalTo: toastContainer.bottomAnchor, constant: -12)
        ])
        
        // Add to window
        window.addSubview(toastContainer)
        toastContainer.translatesAutoresizingMaskIntoConstraints = false
        NSLayoutConstraint.activate([
            toastContainer.centerXAnchor.constraint(equalTo: window.centerXAnchor),
            toastContainer.centerYAnchor.constraint(equalTo: window.centerYAnchor),
            toastContainer.leadingAnchor.constraint(greaterThanOrEqualTo: window.leadingAnchor, constant: 20),
            toastContainer.trailingAnchor.constraint(lessThanOrEqualTo: window.trailingAnchor, constant: -20)
        ])
        
        // Initial animation
        toastContainer.alpha = 0
        UIView.animate(withDuration: 0.3, animations: {
            toastContainer.alpha = 1
        }) { _ in
            // Auto dismiss after 2 seconds
            UIView.animate(withDuration: 0.3, delay: 2.0, options: [], animations: {
                toastContainer.alpha = 0
            }) { _ in
                toastContainer.removeFromSuperview()
            }
        }
    }
}
